# Python version requirement: >= 3.8
# Core Flask dependencies
flask>=3.0.0
flask-cors>=4.0.0
werkzeug>=3.0.0
wqb
# HTTP requests
requests>=2.28.0

# Data processing
pandas>=2.0.0

# Jupyter and notebook support
jupyter>=1.0.0
notebook>=7.0.0
ipykernel>=6.20.0

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0
PyMuPDF>=1.23.0

# Document processing
python-docx>=1.0.0
docx2txt>=0.8
striprtf>=0.0.20

# Text processing and parsing
lxml>=4.9.0
beautifulsoup4>=4.12.0

# Coze API integration
cozepy>=0.4.0

# Optional dependencies for enhanced functionality
# Image processing (if needed for future features)
# Pillow>=10.0.0

# Development and testing (optional)
# pytest>=7.0.0
# pytest-cov>=4.0.0

# Performance monitoring (optional)
# psutil>=5.9.0 