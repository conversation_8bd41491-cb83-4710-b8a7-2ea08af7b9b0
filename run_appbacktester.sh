#!/bin/bash

echo "==================================="
echo "BRAIN Expression Template Decoder"
echo "==================================="
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "Error: Python is not installed!"
    echo "Please install Python from https://www.python.org/"
    exit 1
fi

# Use python3 if available, otherwise fall back to python
if command -v python3 &> /dev/null; then
    PYTHON_CMD=python3
else
    PYTHON_CMD=python
fi

echo "Starting the application..."
echo "The app will automatically install any missing dependencies."
echo

# 设置Flask运行端口
export FLASK_RUN_PORT=5001

ps aux | grep 'python.*appwqb.py' | grep -v grep | awk '{print $2}' | xargs -I {} kill -9 {} 2>/dev/null
# Run the Flask application
# 将启动命令中的文件名更新
nohup $PYTHON_CMD appbacktester.py &

# 循环监听5001端口，直到端口被占用
while ! lsof -i :5001 > /dev/null 2>&1; do
    echo "Waiting for port 5001 to be occupied..."
    sleep 1
 done
 echo "Port 5001 is now occupied"

#open app url
open http://localhost:5001


# Check if the app exited with an error
if [ $? -ne 0 ]; then
    echo
    echo "Application exited with an error."
    read -p "Press Enter to continue..."
fi
