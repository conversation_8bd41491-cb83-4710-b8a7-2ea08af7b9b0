# 标准库导入
import datetime
import json
import os
import queue
import subprocess
import sys
import threading
import time

# Flask导入（按字母顺序）
from flask import Blueprint, jsonify, render_template, request, Response, send_file, stream_with_context

# 删除此行：from appwqb import brain_sessions

# 创建回测蓝图
alpha_backtester_bp = Blueprint('alpha_backtester', __name__)

# ===== 移动以下函数到此处 =====
# 添加最新结果文件路径
LATEST_RESULT_PATH = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')), 'static', 'latest_result.json')
# 准备参数
# 获取项目根目录路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))

# 使用新的alpha_backtester_session.py脚本
script_path = os.path.join(project_root, 'alpha_backtester_session.py')

# 添加读取最新结果路径的函数
def get_latest_result_path():
    if os.path.exists(LATEST_RESULT_PATH):
        try:
            with open(LATEST_RESULT_PATH, 'r') as f:
                data = json.load(f)
                url_path = data.get('file_path')
                if url_path and url_path.startswith('/static/'):
                    # 将URL路径转换为服务器文件系统路径
                    relative_path = url_path[len('/static/'):]
                    # 直接使用project_root（appwqb目录）拼接静态路径
                    return os.path.join(project_root, 'static', relative_path)
                return url_path
        except Exception as e:
            print(f"读取latest_result.json失败: {str(e)}")

# 添加更新最新结果路径的函数
def update_latest_result(file_path):
    static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
    relative_path = os.path.relpath(file_path, static_dir)
    
    result_data = {
        "file_path": f"/static/backtest_results/{os.path.basename(file_path)}",
        "timestamp": datetime.datetime.now().isoformat(),
        "results_count": 1
    }
    
    with open(os.path.join(static_dir, 'latest_result.json'), 'w') as f:
        json.dump(result_data, f)
# ===== 函数移动结束 =====

# 创建队列存储日志
log_queue = queue.Queue()
process = None
log_thread = None
should_stop_thread = False
result_file_path = get_latest_result_path()  # 现在函数已定义，可以正常调用
temp_file_path = None  # 存储临时文件路径

# 回测主页
@alpha_backtester_bp.route('/')
def alpha_backtester():
    """Alpha回测页面"""
    return render_template('alpha_backtester.html')

# 日志收集函数
def collect_logs(proc, q):
    global should_stop_thread, result_file_path
    for line in iter(proc.stdout.readline, b''):
        if should_stop_thread:
            break
        decoded_line = line.decode('utf-8', errors='replace')
        
        # 检查是否包含结果文件路径信息
        if "结果已保存到:" in decoded_line and ".csv" in decoded_line:
            try:
                # 提取CSV文件路径
                file_path = decoded_line.split("结果已保存到:")[1].strip()
                if os.path.exists(file_path):
                    # 计算相对于static目录的路径并转换为URL
                    static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
                    relative_path = os.path.relpath(file_path, static_dir)
                    # 使用replace方法处理路径分隔符
                    result_file_path = '/static/' + relative_path.replace('\\', '/')
                    update_latest_result(file_path)  # 更新latest_result.json
                    print(f"找到结果文件: {result_file_path}")
            except Exception as e:
                print(f"提取结果文件路径时出错: {str(e)}")
        
        q.put(decoded_line)
    proc.stdout.close()
    
    # 清理临时文件
    if temp_file_path and os.path.exists(temp_file_path):
        try:
            os.remove(temp_file_path)
            print(f"已删除临时文件: {temp_file_path}")
        except Exception as e:
            print(f"删除临时文件时出错: {str(e)}")

# 运行回测API
# 修改导入
from flask import current_app

# 修改会话访问代码
@alpha_backtester_bp.route('/api/run', methods=['POST'])
def run_backtest():
    # 获取请求数据
    data = request.get_json()
    # 添加详细请求日志
    current_app.logger.info(f"[DEBUG] 接收到回测请求: {json.dumps(data, ensure_ascii=False)}")
    current_app.logger.info(f"Received POST request to /api/run with data: {json.dumps(data.get('params',{}), ensure_ascii=False)}")

    global process, log_thread, log_queue, should_stop_thread, result_file_path, temp_file_path
    
    # 如果已有进程在运行，终止它
    if process and process.poll() is None:
        process.terminate()
        should_stop_thread = True
        if log_thread and log_thread.is_alive():
            log_thread.join(1)
        # 清空队列
        while not log_queue.empty():
            log_queue.get()
    
    # 重置停止标志和结果文件路径
    should_stop_thread = False
    result_file_path = None
    
    try:
        # 从请求中获取回测参数
        data = request.json
        alphas = data.get('params',{}).get('factors', [])
        current_app.logger.info(f"[DEBUG] 获取到 {len(alphas)} 个因子表达式")

        # 获取认证信息
        # session_id = auth_data.get('session_id', '')  # 删

        # 从请求头或会话中获取session_id
        session_id = request.headers.get('Session-ID')

        # 添加调试打印
        current_app.logger.info(f"[DEBUG] Received Session-ID: {session_id}")

        request_data = request.get_json()
        username = request_data.get('username')
        password = request_data.get('password')
        current_app.logger.info(f"[DEBUG] 提取到认证信息 - 用户名: {username}")
        
        if not username or not password:
            return jsonify({'success': False, 'error': '会话中缺少认证信息'}), 400

        # 检查参数有效性
        if not alphas:
            return jsonify({'success': False, 'error': '请至少输入一个因子表达式'}), 400
            
        # 转换因子数据为需要的格式
        transformed_alphas = []
        for i, alpha in enumerate(alphas):
            expr = alpha.get('alphaExpression', '')
            region = alpha.get('region', 'USA')
            universe = alpha.get('universe', 'TOP3000')
            # 添加参数转换日志
            current_app.logger.info(f"[DEBUG] 转换因子 {i+1}/{len(alphas)}: 表达式={expr[:30]}..., 区域={region}, 股票池={universe}")
            delay = int(alpha.get('delay', 1))
            decay = int(alpha.get('decay', 6))
            neutralization = alpha.get('neutralization', 'SUBINDUSTRY')
            truncation = float(alpha.get('truncation', 0.08))
            pasteurization = alpha.get('pasteurization', 'ON')
            nan_handling = alpha.get('nanHandling', 'ON')
            max_trade = alpha.get('maxTrade', 'OFF')
            # 将前端传递的字符串（如"true"）转换为布尔值
            visualization_str = alpha.get('visualization', 'false').lower()
            visualization = visualization_str == 'true'
            
            transformed_alpha = {
                'type': 'REGULAR',
                'settings': {
                    'instrumentType': 'EQUITY',
                    'region': region,
                    'universe': universe,
                    'delay': delay,
                    'decay': decay,
                    'neutralization': neutralization,
                    'truncation': truncation,
                    'pasteurization': pasteurization,
                    'unitHandling': 'VERIFY',
                    'nanHandling': nan_handling,
                    'language': 'FASTEXPR',
                    'visualization': visualization,
                    'testPeriod': "P0Y",
                    'maxTrade': max_trade
                },
                'regular': expr
            }
            transformed_alphas.append(transformed_alpha)
        
        # 将转换后的参数写入临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            json.dump(transformed_alphas, f)
            print(f"alphas: {transformed_alphas}")
            temp_file_path = f.name
            f.flush()
            f.close()

        # 创建结果文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        # 创建结果目录路径（直接基于appwqb根目录）
        result_dir = os.path.join(project_root, 'static', 'backtest_results')
        # 确保目录存在
        os.makedirs(result_dir, exist_ok=True)
        # 生成结果文件路径
        result_file = os.path.join(result_dir, f"backtest_results_{timestamp}.csv")
        
        # 设置环境变量传递敏感参数
        os.environ['BACKTEST_USERNAME'] = username
        os.environ['BACKTEST_PASSWORD'] = password
        
        # 转义路径参数
        temp_file_escaped = json.dumps(temp_file_path)
        result_file_escaped = json.dumps(result_file)
        project_root_escaped = json.dumps(project_root)
        
        # 添加项目根目录到Python路径，确保能找到alpha_backtester_session模块
        cmd = [
            sys.executable,
            '-c',
            f"import os, sys, json; sys.path.append({project_root_escaped});"
            f"from alpha_backtester_session import run_backtest_with_session;"
            f"run_backtest_with_session({temp_file_escaped}, os.environ['BACKTEST_USERNAME'], os.environ['BACKTEST_PASSWORD'], {result_file_escaped})"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            bufsize=1,
            universal_newlines=False
        )
        
        # 启动日志收集线程
        log_thread = threading.Thread(target=collect_logs, args=(process, log_queue))
        log_thread.daemon = True
        log_thread.start()
        
        return jsonify({
            'success': True, 
            'message': '回测已启动',
            'process_id': str(process.pid)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 日志流API
@alpha_backtester_bp.route('/api/logs')
def stream_logs():
    def generate():
        global result_file_path
        while True:
            # 检查进程是否还在运行
            if process and process.poll() is not None and log_queue.empty():
                # 进程已结束且队列为空
                exit_code = process.poll()
                # 返回结果文件路径（如果有）
                if result_file_path and os.path.exists(result_file_path):
                    yield f"data: {{\"type\": \"complete\", \"exitCode\": {exit_code}, \"results\": \"{result_file_path}\"}}\n\n"
                else:
                    yield f"data: {{\"type\": \"complete\", \"exitCode\": {exit_code}}}\n\n"
                break
                
            # 获取日志内容
            try:
                line = log_queue.get(timeout=1)
                # 转换为JSON格式并转义特殊字符
                escaped_line = line.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
                yield f"data: {{\"type\": \"log\", \"message\": \"{escaped_line}\"}}\n\n"
            except queue.Empty:
                # 队列为空则发送保持连接的消息
                yield f"data: {{\"type\": \"keepalive\", \"keepalive\": true}}\n\n"
                time.sleep(0.5)
    
    return Response(stream_with_context(generate()), mimetype='text/event-stream')

# 获取结果文件API
@alpha_backtester_bp.route('/api/results')
def get_results():
    global result_file_path
    # 如果result_file_path为空，尝试从latest_result.json读取
    if not result_file_path:
        result_file_path = get_latest_result_path()
        
    if result_file_path and os.path.exists(result_file_path):
        return send_file(
            result_file_path,
            mimetype='text/plain',
            as_attachment=False,  # 修改为False，允许浏览器内联显示
            download_name=os.path.basename(result_file_path)
        )
    else:
        return jsonify({'success': False, 'error': '结果文件不存在'}), 404

# 停止回测API
@alpha_backtester_bp.route('/api/stop', methods=['POST'])
def stop_backtest():
    global process, should_stop_thread
    
    if process and process.poll() is None:
        process.terminate()
        should_stop_thread = True
        return jsonify({'status': 'stopped', 'message': '回测已停止'})
    else:
        return jsonify({'status': 'error', 'message': '没有正在运行的回测'}), 400

@alpha_backtester_bp.route('/api/clean_old_results', methods=['POST'])
def clean_old_results():
    try:
        # 获取项目根目录
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))
        
        # 目标目录
        results_dir = os.path.join(project_root, 'static', 'backtest_results')
        
        # 检查目录是否存在，如果不存在则创建
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)
            return jsonify({
                'success': True, 
                'message': '结果目录不存在，已创建新目录'
            })
        
        # 获取当前时间
        now = time.time()
        
        # 保留最近24小时的文件
        time_threshold = now - (24 * 60 * 60)  # 24小时前
        
        # 删除旧文件
        deleted_files = 0
        for filename in os.listdir(results_dir):
            file_path = os.path.join(results_dir, filename)
            
            # 跳过目录
            if os.path.isdir(file_path):
                continue
            
            # 检查文件是否是CSV或JSON
            if not (filename.endswith('.csv') or filename.endswith('.json')):
                continue
            
            # 获取文件修改时间
            file_mtime = os.path.getmtime(file_path)
            
            # 删除超过24小时的文件
            if file_mtime < time_threshold and filename != 'latest_result.json':
                try:
                    os.remove(file_path)
                    deleted_files += 1
                except Exception as e:
                    print(f"删除文件 {filename} 失败: {e}")
        
        return jsonify({
            'success': True, 
            'message': f'已删除 {deleted_files} 个旧结果文件'
        })
    
    except Exception as e:
        return jsonify({
            'success': False, 
            'error': str(e)
        }), 500

# 自定义选项文件路径
CUSTOM_OPTIONS_PATH = os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')), 'static', 'custom_options.json')

@alpha_backtester_bp.route('/api/custom-options', methods=['GET'])
def get_custom_options():
    """获取保存的自定义选项"""
    try:
        # 确保文件存在
        if not os.path.exists(CUSTOM_OPTIONS_PATH):
            with open(CUSTOM_OPTIONS_PATH, 'w') as f:
                json.dump({'neutralization': [], 'region': [], 'universe': []}, f)
                
        # 读取文件内容
        with open(CUSTOM_OPTIONS_PATH, 'r') as f:
            options = json.load(f)
            return jsonify(options)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@alpha_backtester_bp.route('/api/custom-options', methods=['POST'])
def save_custom_options():
    """保存自定义选项到服务器文件"""
    try:
        data = request.json
        with open(CUSTOM_OPTIONS_PATH, 'w') as f:
            json.dump(data, f, indent=2)
        return jsonify({'success': True, 'message': '自定义选项已保存'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
