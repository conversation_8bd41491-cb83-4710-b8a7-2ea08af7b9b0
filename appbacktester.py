"""
BRAIN Expression Template Decoder - Flask Web Application
A complete web application for decoding string templates with WorldQuant BRAIN integration
"""

# Auto-install dependencies if missing
import subprocess
import sys
import os

brain_sessions = {}

print("🧠 BRAIN API integration configured!")

from blueprints import alpha_backtester_bp


def install_requirements():
    """Install required packages from requirements.txt if they're missing"""
    print("🔍 Checking and installing required dependencies...")
    print("📋 Verifying packages needed for BRAIN Expression Template Decoder...")
    
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Check if requirements.txt exists in the script directory
    req_file = os.path.join(script_dir, 'requirements.txt')
    if not os.path.exists(req_file):
        print("❌ Error: requirements.txt not found!")
        print(f"Looking for: {req_file}")
        return False
    
    # Read mirror configuration if it exists
    mirror_url = 'https://pypi.tuna.tsinghua.edu.cn/simple'  # Default to Tsinghua
    mirror_config_file = os.path.join(script_dir, 'mirror_config.txt')
    
    if os.path.exists(mirror_config_file):
        try:
            with open(mirror_config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and line.startswith('http'):
                        mirror_url = line
                        break
        except Exception as e:
            print(f"Warning: Could not read mirror configuration: {e}")
    
    # Try to import the main packages to check if they're installed
    packages_to_check = {
        'flask': 'flask',
        'flask_cors': 'flask-cors',
        'requests': 'requests',
        'pandas': 'pandas',
        'PyPDF2': 'PyPDF2',
        'docx': 'python-docx',
        'pdfplumber': 'pdfplumber',
        'fitz': 'PyMuPDF',
        'cozepy': 'cozepy',
        'lxml': 'lxml',
        'bs4': 'beautifulsoup4'
    }
    
    missing_packages = []
    for import_name, pip_name in packages_to_check.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(pip_name)
            print(f"Missing package: {pip_name} (import name: {import_name})")
    
    if missing_packages:
        print(f"⚠️  Missing packages detected: {', '.join(missing_packages)}")
        print("📦 Installing dependencies from requirements.txt...")
        print(f"🌐 Using mirror: {mirror_url}")
        
        try:
            # Install all requirements using configured mirror
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-i', mirror_url,
                '-r', req_file
            ])
            print("✅ All dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Error: Failed to install dependencies using {mirror_url}")
            print("🔄 Trying with default PyPI...")
            try:
                # Fallback to default PyPI
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', req_file])
                print("✅ All dependencies installed successfully!")
                return True
            except subprocess.CalledProcessError:
                print("❌ Error: Failed to install dependencies. Please run manually:")
                print(f"  {sys.executable} -m pip install -i {mirror_url} -r requirements.txt")
                return False
    else:
        print("✅ All required dependencies are already installed!")
        return True

# Check and install dependencies before importing
# This will run every time the module is imported, but only install if needed
def check_and_install_dependencies():
    """Check and install dependencies if needed"""
    if not globals().get('_dependencies_checked'):
        if install_requirements():
            globals()['_dependencies_checked'] = True
            return True
        else:
            print("\nPlease install the dependencies manually and try again.")
            return False
    return True

# Always run the dependency check when this module is imported
print("🚀 Initializing BRAIN Expression Template Decoder...")
if not check_and_install_dependencies():
    if __name__ == "__main__":
        sys.exit(1)
    else:
        print("⚠️  Warning: Some dependencies may be missing. Please run 'pip install -r requirements.txt'")
        print("🔄 Continuing with import, but some features may not work properly.")

# Now import the packages
try:
    from flask import Flask, render_template, request, jsonify, session as flask_session
    from flask_cors import CORS
    import requests
    import json
    import time
    import os
    from datetime import datetime
    print("📚 Core packages imported successfully!")
except ImportError as e:
    print(f"❌ Failed to import core packages: {e}")
    print("Please run: pip install -r requirements.txt")
    if __name__ == "__main__":
        sys.exit(1)
    raise

# 确保静态路径配置正确
app = Flask(__name__, static_folder='static', static_url_path='/static')
app.secret_key = 'brain_template_decoder_secret_key_change_in_production'
CORS(app)

# 添加日志配置
import logging
from logging import StreamHandler  # 替换原有的 from logging.handlers import StreamHandler

# 配置INFO级别日志
handler = StreamHandler()
handler.setLevel(logging.INFO)
app.logger.addHandler(handler)
app.logger.setLevel(logging.INFO)

print("🌐 Flask application initialized with CORS support!")

# BRAIN API configuration
BRAIN_API_BASE = 'https://api.worldquantbrain.com'

# Store BRAIN sessions (in production, use proper session management like Redis)

print("🧠 BRAIN API integration configured!")

def sign_in_to_brain(username, password):
    """Sign in to BRAIN API with retry logic"""
    session = requests.Session()
    session.auth = (username, password)
    
    retry_count = 0
    max_retries = 3
    
    while retry_count < max_retries:
        try:
            response = session.post(f'{BRAIN_API_BASE}/authentication')
            response.raise_for_status()
            
            # 移除real_session_id相关代码
            print(f"Authentication successful.")
            return session  # 仅返回session对象
        except requests.HTTPError as e:
            print(f"HTTP error occurred: {e}. Retrying...")
            retry_count += 1
            if retry_count >= max_retries:
                raise
            time.sleep(2)
        except Exception as e:
            print(f"Error during authentication: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                raise
            time.sleep(2)

# Routes
@app.route('/')
def index():
    """Main application page"""
    return render_template('alpha_backtester.html')

@app.route('/api/authenticate', methods=['POST'], endpoint='api_authenticate')
def authenticate():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        session = sign_in_to_brain(username, password)
        
        session_id = f"{username}_{int(time.time())}"
        brain_sessions[session_id] = {
            'session': session,
            'timestamp': time.time(),
            'username': username  # 添加用户名存储
        }
        
        return jsonify({
            'success': True,
            'session_id': session_id
        })
    except requests.HTTPError as e:
        if e.response.status_code == 401:
            return jsonify({'error': 'Invalid username or password'}), 401
        else:
            return jsonify({'error': f'Authentication failed: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'Authentication error: {str(e)}'}), 500

@app.route('/api/logout', methods=['POST'])
def logout():
    """Logout and clean up session"""
    try:
        session_id = request.headers.get('Session-ID') or flask_session.get('brain_session_id')
        if session_id and session_id in brain_sessions:
            del brain_sessions[session_id]
        
        if 'brain_session_id' in flask_session:
            flask_session.pop('brain_session_id')
        
        return jsonify({'success': True, 'message': 'Logged out successfully'})
        
    except Exception as e:
        return jsonify({'error': f'Logout failed: {str(e)}'}), 500

# Register blueprints
app.register_blueprint(alpha_backtester_bp, url_prefix='/alpha-backtester')

print("🔧 All blueprints registered successfully!")

@app.route('/api/userinfo', methods=['GET'], endpoint='api_userinfo')
def get_userinfo():
    try:
        session_id = request.headers.get('Session-ID')
        if not session_id:
            return jsonify({'error': 'Session-ID header missing'}), 401

        # 移除从app.config获取的错误代码
        # brain_sessions = app.config.get('brain_sessions', {})
        
        if session_id not in brain_sessions:
            return jsonify({'error': 'Invalid or expired session'}), 401

        user_info = {
            'username': brain_sessions[session_id]['username'],
            'timestamp': brain_sessions[session_id]['timestamp']
        }

        return jsonify(user_info)
    except Exception as e:
        app.logger.error(f"User info fetch failed: {str(e)}")
        return jsonify({'error': f'Failed to fetch user info: {str(e)}'}), 500

@app.route('/api/status', methods=['GET'])
def check_status():
    """Check if session is still valid"""
    try:
        session_id = request.headers.get('Session-ID') or flask_session.get('brain_session_id')
        if not session_id or session_id not in brain_sessions:
            return jsonify({'valid': False})
        
        session_info = brain_sessions[session_id]
        # Check if session is not too old (24 hours)
        if time.time() - session_info['timestamp'] > 86400:
            del brain_sessions[session_id]
            return jsonify({'valid': False})
        
        return jsonify({
            'valid': True,
            'username': session_info['username']
        })
        
    except Exception as e:
        return jsonify({'error': f'Status check failed: {str(e)}'}), 500


if __name__ == '__main__':
    print("Starting BRAIN Expression BackTester Web Application...")
    # 从环境变量获取端口，默认5001
    port = int(os.environ.get('FLASK_RUN_PORT', 5001))
    print(f"Application will run on http://localhost:{port}")
    print("BRAIN API integration included - no separate proxy needed!")
    # 禁用reloader解决多进程全局变量隔离问题
    app.run(debug=True, host='0.0.0.0', port=port, use_reloader=False)