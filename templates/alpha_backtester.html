<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alpha因子回测器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }

        /* 添加到现有样式 */
    /* 标签页容器样式 */
    .factor-tabs-container {
        margin: 10px 0;
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
    }
    
    .factor-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .factor-tab {
        padding: 4px 8px;  /* 增加左右内边距提供更多空间 */
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 3px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        font-size: 10px;  /* 减小字体大小 */
        max-width: 11%;  /* 从 9% 增加到 10%，调宽约 11% */
    }
    
    .factor-tab:hover {
        background-color: #e0e0e0;
    }
    
    .factor-tab.active {
        background-color: #ffffff;  /* 白色背景 */
        color: #0066cc;  /* 蓝色文字 */
        border-color: #0066cc;
        font-weight: bold;
    }
    
    .factor-tab-name {
        margin-right: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .factor-tab-close {
        font-size: 10px;  /* 减小关闭按钮字体 */
        width: 12px;  /* 减小关闭按钮宽度 */
        height: 12px;  /* 减小关闭按钮高度 */
        line-height: 12px;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(0,0,0,0.1);
        cursor: pointer;
        transition: all 0.2s ease;
        margin-left: auto; /* 添加自动左边距将按钮推到右侧 */
    }
    
    .factor-tab.active .factor-tab-close {
        background-color: rgba(0,102,204,0.1);  /* 配合蓝色主题 */
    }
    
    .factor-tab-close:hover {
        background-color: rgba(0,0,0,0.2);
    }
    
    .factor-tab.active .factor-tab-close:hover {
        background-color: rgba(0,102,204,0.3);  /* 配合蓝色主题 */
    }
    
    .separator {
        border-top: 1px solid #ccc; 
        margin: 15px 0;
    }
    
    .button-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .login-status {
            font-size: 14px;
            color: white;
            display: flex;
            align-items: center;
        }
        
        #login-text {
            margin-right: 5px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content-wrapper {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .left-column {
            flex: 1;
            padding: 10px;
            background-color: white;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            max-height: 100vh;  /* 设置最大高度为视口高度的60% */
            margin-bottom: 10px;  /* 添加底部边距，避免与其他元素紧贴 */
        }
        
        .right-column {
            flex: 1;
            padding: 10px;
            background-color: white;
            overflow-y: auto;
        }
        
        .alpha-input-group {
            margin-bottom: 5px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        /* 网格布局 */
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;  /* 从10px减小到5px */
            margin-bottom: 3px;  /* 从5px减小到3px */
        }
        
        /* 对于Alpha表达式使用全宽 */
        .full-width {
            grid-column: 1 / -1;
        }
        
        .input-group {
            margin-bottom: 3px;  /* 从5px减小到3px */
        }
        
        .input-group label {
            display: block;
            margin-bottom: 2px;  /* 从5px减小到2px */
            font-weight: bold;
            font-size: 12px;  /* 新增：减小标签字体大小 */
        }
        
        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 4px;  /* 从6px减小到4px */
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 12px;  /* 从13px减小到12px */
        }
        
        .input-group textarea {
            min-height: 100px;
            font-family: monospace;
        }
        
        .custom-input-row {
            display: flex;
            margin-top: 5px;
        }
        
        .custom-input-row input {
            flex: 1;
            margin-right: 5px;
        }
        
        .custom-input-row button {
            padding: 8px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        /* 选项容器样式 */
        .select-container {
            position: relative;
        }
        
        /* 选项样式 */
        .option-item {
            display: flex;
            align-items: center;
            padding: 5px 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin: 3px 0;
            border: 1px solid #ddd;
        }
        
        .option-text {
            flex: 1;
        }
        
        .delete-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }

        .form-input {
            padding: 5px 8px; /* 与下拉框保持一致的内边距 */
            font-size: 14px; /* 与下拉框字体大小匹配 */
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .button-group {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        
        .status-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .badge-warning {
            background-color: #f39c12;
            color: white;
        }
        
        .badge-primary {
            background-color: #3498db;
            color: white;
        }
        
        .badge-success {
            background-color: #2ecc71;
            color: white;
        }
        
        .badge-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .progress-container {
            flex-grow: 1;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.3s;
            text-align: center;
            color: white;
            font-weight: bold;
            line-height: 20px;
        }
        
        .results-container {
            margin-top: 20px;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .results-table th, .results-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .results-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.result-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}
.result-table th, .result-table td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}
.result-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}
.result-table tr:nth-child(even) {
    background-color: #f9f9f9;
}
.long-text {
    max-width: 400px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-container {
    min-height: 350px;
    max-height: 350px; /* 从300px增加到600px */
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
    font-family: monospace;
    margin-bottom: 20px;
}
        
        .log-entry {
            margin-bottom: 5px;
            line-height: 1.5;
        }
        
        .log-time {
            color: #7f8c8d;
            margin-right: 10px;
        }
        
        .log-text {
            white-space: pre-wrap;
        }
        
        .log-error {
            color: #e74c3c;
        }
        
        .log-warning {
            color: #f39c12;
        }
        
        .log-success {
        color: #2ecc71;
    }
    
    .add-factor-btn {
        padding: 8px 15px;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 10px;
    }
    
    .add-factor-btn:hover {
        background-color: #218838;
    }
    
    .add-factor-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }
    
    /* 添加到现有样式表中 */
    .login-status {
        display: flex;
        align-items: center;
    }
    
    .login-status span {
        color: white;
        margin-right: 10px;
    }
    
    .main-page-btn {
        background-color: #27ae60;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
        transition: background-color 0.3s;
    }
    
    .main-page-btn:hover {
        background-color: #2ecc71;
    }
    
    .main-page-btn:disabled {
        background-color: #95a5a6;
        cursor: not-allowed;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>

<script>
    // 通知函数
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#667eea'};
            color: white;
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后移除
        setTimeout(() => {
            notification.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // 添加必要的动画样式
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        </style>
    `);

    function checkBacktestResult(retryCount = 0, maxRetries = 15) { // 大幅增加重试次数
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');
    const downloadBtn = document.getElementById('downloadResultsBtn');
    
    // 确保结果区域可见
    resultsSection.style.display = 'block';
    
    console.log(`检查回测结果 (尝试 ${retryCount + 1}/${maxRetries})`);
    // 超级严格的缓存控制
    fetch('/static/latest_result.json?t=' + new Date().getTime(), {
        cache: 'no-store',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取结果文件失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.file_path) {
                // 获取原始路径并提取相对路径
            let rawPath = data.file_path;
            console.log('原始文件路径:', rawPath);
            // 提取相对于/static/的路径部分
            const staticIndex = rawPath.indexOf('/static/');
            console.log('Static index:', staticIndex);
            if (staticIndex !== -1) {
                rawPath = rawPath.substring(staticIndex);
            }
            console.log('处理后的结果文件路径：', rawPath);
                
            // 验证文件是否存在
            fetch(rawPath, { method: 'HEAD' })
                .then(response => {
                    if (!response.ok) throw new Error('文件不存在');
                    
                    // 文件存在，更新链接
                    const fileName = rawPath.split('/').pop();
                    const csvUrl = rawPath;
                    
                    // 设置下载按钮
                    if (downloadBtn) {
                        downloadBtn.href = csvUrl;
                        downloadBtn.download = fileName || 'backtest_result.csv';
                        downloadBtn.onclick = function(e) {
                            if (!this.href || this.href === '#') {
                                e.preventDefault();
                                alert('下载链接无效，请稍后重试');
                            } else {
                                console.log('下载文件:', this.href);
                            }
                        };
                        console.log('设置下载按钮：', downloadBtn.href, downloadBtn.download);
                    }

                    // 显示结果文件链接
                    const resultLink = document.querySelector('#resultsContent a');
                    const fileNameElement = document.getElementById('resultFileName');
                    if (resultLink && fileNameElement) {
                        resultLink.href = csvUrl;
                        fileNameElement.textContent = `(${fileName})`;
                        console.log('已更新结果链接:', csvUrl);
                    } else {
                        resultsContent.innerHTML = `
                            <p>结果: <a href="${csvUrl}" target="_blank">查看结果文件</a> <span id="resultFileName" class="filename">(${fileName})</span></p>
                        `;
                    }
                })
                .catch(error => {
                    console.error('文件验证失败:', error);
                    resultsContent.innerHTML = `<div class="error">结果文件不存在或无法访问</div>`;
                    if (downloadBtn) {
                        downloadBtn.href = '#';
                        downloadBtn.removeAttribute('download');
                    }
                    if (retryCount < maxRetries) {
                        // 指数退避策略：重试间隔逐渐增加
            const delay = Math.min(3000 * Math.pow(2, retryCount), 15000); // 最多15秒
            console.log(`文件不存在，将在${delay/1000}秒后重试 (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => checkBacktestResult(retryCount + 1, maxRetries), delay);
                    }
                });
            } else {
                throw new Error('未找到结果文件路径');
            }
        })
        .catch(error => {
            console.error('加载结果失败:', error);
            
            // 添加重试逻辑
            if (retryCount < maxRetries) {
                console.log(`将在2秒后重试 (${retryCount + 1}/${maxRetries})`);
                setTimeout(() => checkBacktestResult(retryCount + 1, maxRetries), 3000);
            } else {
                resultsContent.innerHTML = `<div class="error">加载结果失败: ${error.message}</div>`;
            }
        });
}
    // 清理旧的结果文件
    function cleanOldResults() {
        fetch('/alpha-backtester/api/clean_old_results', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
            } else {
                showNotification(data.error, 'error');
            }
        })
        .catch(error => {
            showNotification('清理结果文件失败: ' + error, 'error');
        });
    }

// 加载CSV数据并显示
function loadResultData(csvUrl) {
    const resultsTableContainer = document.getElementById('results-table-container');
    
    fetch(csvUrl)
        .then(response => response.text())
        .then(csvData => {
            if (!csvData.trim()) {
                resultsTableContainer.innerHTML = '<div class="error">CSV文件内容为空</div>';
                return;
            }
            
            const results = Papa.parse(csvData, {
                header: true,
                dynamicTyping: true,
                skipEmptyLines: true
            });
            
            if (results.data && results.data.length > 0) {
                const tableHtml = `
                    <table border="1" class="result-table">
                        <thead>
                            <tr>${Object.keys(results.data[0]).map(key => `<th>${key}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${results.data.map(row => `<tr>${Object.entries(row).map(([key, val]) => {
                                // 数值类型格式化
                                if (typeof val === 'number') {
                                    return `<td>${val.toFixed(4)}</td>`;
                                }
                                return `<td>${val}</td>`;
                            }).join('')}</tr>`).join('')}
                        </tbody>
                    </table>
                `;
                resultsTableContainer.innerHTML = tableHtml;
            } else {
                resultsTableContainer.innerHTML = '<div class="error">结果数据为空</div>';
            }
        })
        .catch(error => {
            resultsTableContainer.innerHTML = `<div class="error">加载结果失败: ${error.message}</div>`;
        });
}

// 每2秒检查一次结果
// setInterval(checkBacktestResult, 2000);

// 修改页面加载事件
window.addEventListener('load', function() {
    // 取消注释，允许页面加载时自动显示结果
    checkBacktestResult();
    // 保存定时器ID
    // resultPollingInterval = setInterval(checkBacktestResult, 2000);
});
</script>
</head>
<body>
    <div class="header">
            <h1>Alpha因子回测器</h1>
            <div class="login-status">
                <button id="connectToBrain" class="btn btn-brain">登录BRAIN</button>
            </div>
        </div>

        <!-- 添加BRAIN登录模态框 -->
        <div id="brainLoginModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>登录BRAIN</h3>
                    <span class="close" onclick="closeBrainLoginModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="brainLoginStatus" class="login-status"></div>
                    <div class="login-form">
                        <div class="input-group">
                            <label for="brainUsername">用户名</label>
                            <input type="text" id="brainUsername" placeholder="请输入用户名">
                        </div>
                        <div class="input-group">
                            <label for="brainPassword">密码</label>
                            <input type="password" id="brainPassword" placeholder="请输入密码">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="cancelBtn" class="btn btn-outline" onclick="closeBrainLoginModal()">取消</button>
                    <button id="loginBtn" class="btn btn-primary" onclick="authenticateBrain()">
                        <span id="loginSpinner" class="spinner" style="display: none;"></span>
                        登录
                    </button>
                </div>
            </div>
        </div>

    <div class="content-wrapper">
        <div class="left-column">
            <div class="alpha-input-group">
                <div class="settings-grid">
                    <div class="input-group">
                        <label for="neutralization-select">NEUTRALIZATION</label>
                        <div class="select-container">
                            <select id="neutralization-select">
                                <option value="none">NONE</option>
                                <option value="market">MARKET</option>
                                <option value="industry">INDUSTRY</option>
                                <option value="subindustry">SUBINDUSTRY</option>
                                <option value="sector">SECTOR</option>
                            </select>
                            <div id="neutralization-options" class="options-container"></div>
                            <div class="custom-input-row">
                                <input type="text" class="form-input custom-neutralization" placeholder="添加自定义中性化...">
                                <button class="add-neutralization-btn">添加</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="region-select">REGION</label>
                        <div class="select-container">
                            <select id="region-select">
                        <option value="USA">USA</option>
                        <option value="CHN">CHN</option>
                        <option value="EUR">EUR</option>
                        <option value="ASI">ASI</option>
                        <option value="GLB">GLB</option>
                        <option value="KOR">KOR</option>
                        <option value="TWN">TWN</option>
                        <option value="HKG">HKG</option>
                        <option value="JPN">JPN</option>
                        <option value="AMR">AMR</option>
                    </select>
                            <div id="region-options" class="options-container"></div>
                            <div class="custom-input-row">
                <input type="text" class="form-input custom-region" placeholder="添加自定义区域...">
                <button class="add-region-btn">添加</button>
            </div>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="universe-select">UNIVERSE</label>
                        <div class="select-container">
                            <select id="universe-select">
                                <!-- 将由JavaScript动态填充 -->
                            </select>
                            <div id="universe-options" class="options-container"></div>
                            <div class="custom-input-row">
                                <input type="text" class="form-input custom-universe" placeholder="添加自定义Universe...">
                                <button class="add-universe-btn">添加</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="decay-input">DECAY</label>
                        <input type="number" id="decay-input" value="5" min="1">
                    </div>
                    
                    <div class="input-group">
            <label for="delay-input">DELAY</label>
            <input type="number" id="delay-input" value="1" min="0" max="1" oninput="this.value = Math.min(Math.max(this.value, 0), 1);">
        </div>
                    
                    <div class="input-group">
                        <label for="truncation-input">TRUNCATION</label>
                        <input type="number" id="truncation-input" value="0.1" min="0" max="1" step="0.01">
                    </div>
                    
                    <div class="input-group">
                        <label for="pasteurization-select">PASTEURIZATION</label>
                        <div class="select-container">
                            <select id="pasteurization-select">
                                <option value="ON">ON</option>
                                <option value="OFF">OFF</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="nan-handling-select">NAN HANDLING</label>
                        <div class="select-container">
                            <select id="nan-handling-select">
                                <option value="ON">ON</option>
                                <option value="OFF">OFF</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="input-group">
                        <label for="max-trade-select">MAX TRADE</label>
                        <div class="select-container">
                            <select id="max-trade-select">
                                <option value="OFF" selected>OFF</option>
                                <option value="ON">ON</option>
                            </select>
                        </div>
                    </div>
                      
                    <!-- 添加visualization选项 -->
                    <div class="input-group">
                        <label for="visualization-select">VISUALIZATION</label>
                        <div class="select-container">
                            <select id="visualization-select">
                                <option value="true">TRUE</option>
                                <option value="false" selected>FALSE</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
        <!-- Alpha表达式输入组 -->
        <div class="input-group">
            <label>Alpha表达式</label>
            <textarea class="alpha-expression" placeholder="输入您的Alpha表达式..." rows="6"></textarea>
        </div>

<!-- 添加按钮行和因子标签页的分隔线 -->
<div class="separator" style="border-top: 1px solid #ccc; margin: 15px 0;"></div>

<!-- 因子标签页容器 - 移到按钮行上面 -->
<div id="factor-tabs-container" class="factor-tabs-container">
    <div id="factor-tabs" class="factor-tabs"></div>
</div>

<!-- 按钮行（添加因子和运行回测在同一行） -->
<div class="button-group" style="display: flex; justify-content: space-between;">
    <div>
        <button id="add-factor-btn" class="btn btn-primary">添加因子</button>
    </div>
    <div style="display: flex; gap: 10px;">
        <button id="run-btn" class="btn btn-primary">运行回测</button>
        <button id="stop-btn" class="btn btn-danger" disabled>停止回测</button>
    </div>
</div>
        </div>
        
        <div class="right-column">
            <h2>回测日志</h2>
            <span id="statusBadge" class="badge badge-secondary">等待中</span>
            <div class="log-container" id="log-container"></div>
            
            <!-- 新增下载结果区域 -->
        <div id="resultsSection" class="mt-4" style="display:none;">
            <div class="card">
                <div class="card-header bg-success text-white">
                <h2 class="mb-0">最近一次回测结果</h2>
            </div>
                <div class="card-body">
                    <div id="resultsContent"></div>
                    <!-- 将表格容器移至结果卡片内 -->
                    <div id="results-table-container" style="margin-top: 15px;"></div>
                    <div class="mt-3">
                        <a id="downloadResultsBtn" href="#" class="btn btn-primary">下载完整结果CSV</a>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
    
    <script>
    // 检查用户是否已登录
function checkUserLogin() {
    // 这里可以从cookie或localStorage检查登录状态
    // 示例: 从localStorage获取登录状态
    return localStorage.getItem('isLoggedIn') === 'true';
}

// 更新登录状态显示
function updateLoginStatus() {
    // 登录状态代码已移除
}


        
        // 全局变量
        let isRunning = false;
        let eventSource = null;
        let customOptions = { neutralization: [], region: [], universe: [] };

        // 存储多个因子数据
        let alphaFactors = [];
        let currentFactorIndex = 0;
        
        // DOM元素 - 删除状态和进度条相关引用
        const runBtn = document.getElementById('run-btn');
        const stopBtn = document.getElementById('stop-btn');
        const logContainer = document.getElementById('log-container');
        const resultsContainer = document.getElementById('resultsSection'); // 修正ID选择器
        const resultsTableContainer = document.getElementById('results-table-container');
        
        // 地区-市场映射字典
        const region_universe_dict = {
            'USA': ['TOP3000', 'TOP1000', 'TOP500', 'TOP200', 'ILLIQUID_MINVOL1M', 'TOPSP500'],
            'GLB': ['TOP3000', 'MINVOL1M','TOPDIV3000'],
            'EUR': ['TOP2500', 'TOP1200', 'TOP800', 'TOP400', 'ILLIQUID_MINVOL1M'],
            'ASI': ['MINVOL1M', 'ILLIQUID_MINVOL1M'],
            'CHN': ['TOP2000U'],
            'KOR': ['TOP600'],
            'TWN': ['TOP500', 'TOP100'],
            'HKG': ['TOP800', 'TOP500'],
            'JPN': ['TOP1600', 'TOP1200'],
            'AMR': ['TOP600']
        };

        // 地区-中性化映射字典
        const region_neutralizations_dict = {
            'USA': ['NONE', 'REVERSION_AND_MOMENTUM','STATISTICAL','CROWDING', 'FAST', 'SLOW', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'SLOW_AND_FAST'],
            'GLB': ['NONE', 'REVERSION_AND_MOMENTUM','STATISTICAL','CROWDING', 'FAST', 'SLOW', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'COUNTRY', 'SLOW_AND_FAST'],
            'EUR': ['NONE', 'REVERSION_AND_MOMENTUM','STATISTICAL','CROWDING', 'FAST', 'SLOW', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'SLOW_AND_FAST'],
            'ASI': ['NONE', 'REVERSION_AND_MOMENTUM','STATISTICAL','CROWDING', 'FAST', 'SLOW', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'SLOW_AND_FAST'],
            'CHN': ['NONE', 'REVERSION_AND_MOMENTUM', 'CROWDING', 'FAST', 'SLOW', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'SLOW_AND_FAST'],
            'KOR': ['NONE', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY'],
            'TWN': ['NONE', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY'],
            'HKG': ['NONE', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY'],
            'JPN': ['NONE', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY'],
            'AMR': ['NONE', 'MARKET', 'SECTOR', 'INDUSTRY', 'SUBINDUSTRY', 'COUNTRY']
        };

function loadCustomOptions() {
    return new Promise((resolve) => {
        try {
            const saved = localStorage.getItem('customOptions');
            if (saved) {
                customOptions = JSON.parse(saved);
                console.log('加载保存的自定义选项:', customOptions);
            } else {
                customOptions = { neutralization: [], region: [], universe: [] };
                console.log('未找到保存的选项，使用默认值');
            }
            resolve();
        } catch (e) {
            console.error('加载自定义选项失败:', e);
            customOptions = { neutralization: [], region: [], universe: [] };
            resolve();
        }
    });
}

        // 页面加载时初始化事件监听器和数据}

function saveCustomOptions() {
    try {
        localStorage.setItem('customOptions', JSON.stringify(customOptions));
        console.log('自定义选项已保存:', customOptions);
    } catch (e) {
        console.error('保存自定义选项失败:', e);
        alert('无法保存自定义选项，请检查浏览器存储权限');
    }
}

document.addEventListener('DOMContentLoaded', function() {    // 从服务器加载自定义选项
    loadCustomOptions().then(() => {
        // 初始化选项容器
        updateNeutralizationOptions(); // 使用新函数
        renderRegionOptions();
        
        // 初始化Universe选项
        updateUniverseOptions();
    })
    
    // 设置按钮事件监听器
    runBtn.addEventListener('click', function() {
        runBacktest()
    });
    stopBtn.addEventListener('click', stopBacktest);
    
    // 设置区域选择事件监听器
    const regionSelect = document.getElementById('region-select');
    regionSelect.addEventListener('change', function() {
        updateUniverseOptions();
        updateNeutralizationOptions(); // 添加此行
    }); 
    
    // 设置添加自定义选项按钮事件监听器
    const addNeutralizationBtn = document.querySelector('.add-neutralization-btn');
    if (addNeutralizationBtn) {
        addNeutralizationBtn.addEventListener('click', addCustomNeutralization);
    }
    
    const addRegionBtn = document.querySelector('.add-region-btn');
    if (addRegionBtn) {
        addRegionBtn.addEventListener('click', addCustomRegion);
    }
    
    // 添加Universe自定义选项按钮
    const addUniverseBtn = document.querySelector('.add-universe-btn');
    if (addUniverseBtn) {
        addUniverseBtn.addEventListener('click', addCustomUniverse);
    }
    
    // 初始化多因子功能
    initializeFactors();
    
    // 添加登录状态检查
    updateLoginStatus();
    
    // 添加因子按钮事件监听
    const addFactorBtn = document.getElementById('add-factor-btn');
    addFactorBtn.addEventListener('click', addNewFactor);
});
        
        // 渲染中性化选项
        function renderNeutralizationOptions() {
            const select = document.getElementById('neutralization-select');
            select.innerHTML = '';
            
            // 获取当前选中的region
            const regionSelect = document.getElementById('region-select');
            const selectedRegion = regionSelect.value;
            
            // 添加默认选项
            const defaultOptions = region_neutralizations_dict[selectedRegion] || [];
            defaultOptions.forEach(option => {
                const opt = document.createElement('option');
                opt.value = option;
                opt.textContent = option;
                select.appendChild(opt);
            });
            
            // 添加自定义选项
            customOptions.neutralization.forEach(option => {
                const opt = document.createElement('option');
                opt.value = option;
                opt.textContent = option;
                select.appendChild(opt);
                
                const deleteBtn = document.createElement('button');
                deleteBtn.textContent = '×';
                deleteBtn.className = 'delete-option';
                deleteBtn.addEventListener('click', () => {
                    customOptions.neutralization = customOptions.neutralization.filter(item => item !== option);
                    saveCustomOptions();
                    renderNeutralizationOptions();
                });
                opt.appendChild(deleteBtn);
            });
        }
        
        // 渲染区域选项
        function renderRegionOptions() {
            const regionSelect = document.getElementById('region-select');
            const optionsContainer = document.getElementById('region-options');
            regionSelect.innerHTML = '';
            optionsContainer.innerHTML = '';

            // 添加默认区域选项
            const defaultRegions = Object.keys(region_universe_dict);
            defaultRegions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                regionSelect.appendChild(optionElement);
            });

            // 添加自定义区域选项
            customOptions.region.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option + ' (custom)';
                optionElement.className = 'custom-option';
                regionSelect.appendChild(optionElement);

                // 添加删除按钮
                const optionItem = document.createElement('div');
                optionItem.className = 'option-item';
                optionItem.innerHTML = `
                    <span>${option} (custom)</span>
                    <button class="remove-btn" data-type="region" data-value="${option}">删除</button>
                `;
                optionsContainer.appendChild(optionItem);
            });

            // 为删除按钮添加事件监听器
            const removeButtons = optionsContainer.querySelectorAll('.remove-btn');
            removeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    const value = this.getAttribute('data-value');
                    removeCustomOption(type, value);
                });
            });
        }
        
        // 渲染Universe选项
        function renderUniverseOptions(defaultUniverse) {
            const universeSelect = document.getElementById('universe-select');
            const optionsContainer = document.getElementById('universe-options');
            universeSelect.innerHTML = '';
            optionsContainer.innerHTML = '';
            
            // 添加默认选项
            if (defaultUniverse) {
                defaultUniverse.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    universeSelect.appendChild(optionElement);
                });
            }
            
            // 添加自定义选项
            customOptions.universe.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option + ' (custom)';
                optionElement.className = 'custom-option';
                universeSelect.appendChild(optionElement);
                
                // 添加删除按钮
                const optionItem = document.createElement('div');
                optionItem.className = 'option-item';
                optionItem.innerHTML = `
                    <span>${option} (custom)</span>
                    <button class="remove-btn" data-type="universe" data-value="${option}">删除</button>
                `;
                optionsContainer.appendChild(optionItem);
            });
            
            // 为删除按钮添加事件监听器
            const removeButtons = optionsContainer.querySelectorAll('.remove-btn');
            removeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    const value = this.getAttribute('data-value');
                    removeCustomOption(type, value);
                });
            });
        }
        
        // 移除自定义选项
        function removeCustomOption(type, value) {
            const index = customOptions[type].indexOf(value);
            if (index !== -1) {
                customOptions[type].splice(index, 1);
                if (type === 'neutralization') {
                    renderNeutralizationOptions();
                } else if (type === 'region') {
                    renderRegionOptions();
                    updateUniverseOptions();
                } else if (type === 'universe') {
                    const regionSelect = document.getElementById('region-select');
                    const selectedRegion = regionSelect.value;
                    renderUniverseOptions(region_universe_dict[selectedRegion]);
                }
                saveCustomOptions(); // 保存到localStorage
            }
        }
        
        // 根据选择的区域更新Universe选项
        function updateUniverseOptions() {
            try {
                const regionSelect = document.getElementById('region-select');
                const selectedRegion = regionSelect.value;
                console.log('更新Universe选项，当前地区:', selectedRegion);
                
                if (selectedRegion && region_universe_dict[selectedRegion]) {
                    renderUniverseOptions(region_universe_dict[selectedRegion]);
                } else {
                    console.warn('未找到地区对应的Universe选项:', selectedRegion);
                    renderUniverseOptions([]);
                }
            } catch (error) {
                console.error('更新Universe选项失败:', error);
            }
        }
        
        // 添加中性化选项更新函数
        function updateNeutralizationOptions() {
    renderNeutralizationOptions(); // 移除参数，使用无参数调用
}
        
        // 添加自定义中性化选项
function addCustomNeutralization() {
    const newOption = document.getElementById('new-neutralization-input').value.trim();
    if (!newOption) return;
    
    // 检查是否已存在（包括默认选项和自定义选项）
    const region = document.getElementById('region-select').value;
    const defaultOptions = region_neutralizations_dict[region] || [];
    if (defaultOptions.includes(newOption) || customOptions.neutralization.includes(newOption)) {
        alert('该选项已存在！');
        return;
    }
    
    customOptions.neutralization.push(newOption);
    saveCustomOptions();
    renderNeutralizationOptions(); // 修改为无参数调用
    document.getElementById('new-neutralization-input').value = '';
}
        
        // 添加自定义区域选项
function addCustomRegion() {
    const customRegionInput = document.querySelector('.custom-region');
    const customValue = customRegionInput.value.trim();
    
    if (!customValue) {
        alert('请输入自定义区域选项名称');
        return;
    }
    
    // 检查是否重复（仅检查customOptions）
    if (!customOptions.region.includes(customValue)) {
        customOptions.region.push(customValue);
        renderRegionOptions();
        customRegionInput.value = '';
        saveCustomOptions(); // 保存到localStorage
    } else {
        alert('该区域选项已存在!');
    }
}
        
        // 添加自定义Universe选项
        function addCustomUniverse() {
            const customUniverseInput = document.querySelector('.custom-universe');
            const customValue = customUniverseInput.value.trim();
            
            if (customValue) {
                // 检查是否重复
                const regionSelect = document.getElementById('region-select');
                const selectedRegion = regionSelect.value;
                const defaultUniverse = region_universe_dict[selectedRegion] || [];
                
                if (!defaultUniverse.includes(customValue) && 
                    !customOptions.universe.includes(customValue)) {
                    customOptions.universe.push(customValue);
                    renderUniverseOptions(defaultUniverse);
                    customUniverseInput.value = '';
                    saveCustomOptions(); // 保存到服务器
                } else {
                    alert('This universe option already exists!');
                }
            }
        }
        
        // 清除结果
        function clearResults() {
            // resultsContainer.style.display = 'none';  // 注释掉这行
            resultsTableContainer.innerHTML = '';
        }
        
        // 清除日志
        function clearLog() {
            logContainer.innerHTML = '';
        }
        
        // 收集Alpha数据
            function collectAlphaData() {
                // 保存当前因子数据
                saveCurrentFactorData();
                
                // 验证每个因子的必填字段
                for (let i = 0; i < alphaFactors.length; i++) {
                    const factor = alphaFactors[i];
                    
                    if (!factor.alphaExpression) {
                        alert(`因子 ${i+1} 需要输入Alpha表达式`);
                        return null;
                    }
                    
                    if (!factor.region) {
                        alert(`因子 ${i+1} 需要选择区域`);
                        return null;
                    }
                    
                    if (!factor.universe) {
                        alert(`因子 ${i+1} 需要选择Universe`);
                        return null;
                    }
                }
                
                // 返回所有因子的数据，visualization已作为每个因子的属性
            return {
                factors: alphaFactors,
                settings: {}
            }
            }
        
        // 运行回测
    function runBacktest() {
            // 收集数据
            const data = collectAlphaData();
            if (!data) return;
            // 清除之前的日志和结果
            clearLog();
            
            // 清理旧的结果文件
            cleanOldResults();
            
            // 重置结果区域
            const resultsSection = document.getElementById('resultsSection');
            const resultsTableContainer = document.getElementById('results-table-container');
            const resultsContent = document.getElementById('resultsContent');
            const downloadResultsBtn = document.getElementById('downloadResultsBtn');
            
            // 确保结果区域可见 - 始终显示结果区域
            resultsSection.style.display = 'block';
            
            // 清空结果内容 - 注释掉这两行
            // resultsTableContainer.innerHTML = '';
            // resultsContent.innerHTML = '';
            
            // 重置下载按钮 - 注释掉这部分
            // if (downloadResultsBtn) {
            //     downloadResultsBtn.href = '#';
            //     downloadResultsBtn.removeAttribute('download');
            // }
            
            // 检查登录状态
    const brainSessionId = localStorage.getItem('brain_session_id');
    if (!brainSessionId) {
        showNotification('请先登录BRAIN', 'error');
        openBrainLoginModal(); // 打开登录模态框
        return;
    }

    // 获取localStorage中的用户名和密码
    const username = localStorage.getItem('username');
    const password = localStorage.getItem('password');
    if (!username || !password) {
        showNotification('登录信息不完整，请重新登录', 'error');
        openBrainLoginModal();
        return;
    }

            // 更新UI状态 - 删除状态和进度条更新
            setRunningState(true);
            
            // 清除之前的日志
            clearLog();
            // clearResults();
            //document.getElementById('resultsSection').style.display = 'none'
            
            // 发送运行请求
            fetch('/alpha-backtester/api/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Session-ID': brainSessionId  // 通过请求头传递session_id
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    params: data
                })})
            .then(response => response.json())
            .then(data => {
                if (data.success) {  // 修改判断条件
                    appendLog('回测已启动: ' + data.message);
                    startLogStream();
                } else {
                    appendLog('启动回测失败: ' + (data.error || data.message));
                    setRunningState(false);
                }
            })
            .catch(error => {
                appendLog('请求错误: ' + error.message);
                setRunningState(false);
            });
        }
        
        // 停止回测
        function stopBacktest() {
            fetch('/alpha-backtester/api/stop', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'stopped') {
                    appendLog('回测已停止');
                    statusBadge.textContent = '已停止';
                    statusBadge.className = 'badge badge-warning';
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                    }
                    setRunningState(false);
                } else {
                    appendLog('停止回测失败: ' + data.message);
                }
            })
            .catch(error => {
                appendLog('停止回测失败: ' + error.message);
            });
        }
        
        // 启动日志流
        function startLogStream() {
            // 如果已有连接先关闭
            if (eventSource) {
                eventSource.close();
            }
            
            // 创建新的EventSource连接，添加蓝图路由前缀
            eventSource = new EventSource('/alpha-backtester/api/logs');
            
            // 设置消息处理函数
            eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                // 处理日志消息
                if (data.type === 'log') {
                    appendLog(data.message);
                }
                
                // 处理进度消息
                if (data.type === 'progress') {
                    updateProgress(data.percentage);
                }
                
                // 处理完成消息
                if (data.type === 'complete') {
                    // 转换字符串路径为预期的对象格式
                    let resultsData;
                    if (typeof data.results === 'string') {
                        resultsData = { file_path: data.results };
                    } else {
                        resultsData = data.results || {};
                    }
                    handleBacktestCompleted(resultsData);
                }
            } catch (e) {
                console.error('消息处理错误:', e);
                appendLog('接收消息错误: ' + e.message);
            }
        };
            
            // 设置错误处理函数
            eventSource.onerror = function(error) {
                console.error('EventSource error:', error);
                appendLog('日志流连接错误');
                setRunningState(false);
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }
            };
        }
        
        // 处理回测完成
        function handleBacktestCompleted(event) {
    eventSource.close();
    isRunning = false;
    runBtn.disabled = false;
    stopBtn.disabled = true;
    statusBadge.textContent = '完成';
    statusBadge.className = 'badge bg-success';
    
    // 添加完整的UI状态重置
    setRunningState(false); // 重置所有控制面板状态
    // 双重保障：立即检查 + 强制刷新
    checkBacktestResult();
    // 5秒后再次强制检查（应对极端延迟情况）
    setTimeout(() => {
        // 强制清除缓存并重新获取
        fetch('/static/latest_result.json?t=' + new Date().getTime() + Math.random(), {
            cache: 'no-store',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        }).then(() => checkBacktestResult());
    }, 5000);
}
        
        // 处理进程退出
        function handleProcessExit(exitCode) {
            // 关闭日志流
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            // 更新UI状态
            setRunningState(false);
            
            // 更新状态显示
            if (exitCode === 0) {
                statusBadge.textContent = '完成';
                statusBadge.className = 'badge badge-success';
                appendLog('进程正常退出');
            } else {
                statusBadge.textContent = '失败';
                statusBadge.className = 'badge badge-danger';
            appendLog(`进程异常退出，退出代码: ${exitCode}`);
        }
    }
        
        // 从日志解析结果
        function parseResults(logText) {
            try {
                // 提取各种指标
                let icMatch = logText.match(/IC:\s+([\d.-]+)/);
                let irMatch = logText.match(/IR:\s+([\d.-]+)/);
                let sharpeMatch = logText.match(/Sharpe:\s+([\d.-]+)/);
                let turnoverMatch = logText.match(/Turnover:\s+([\d.-]+)/);
                let annualReturnMatch = logText.match(/Annual Return:\s+([\d.-]+)%/);
                let maxDrawdownMatch = logText.match(/Max Drawdown:\s+([\d.-]+)%/);
                
                // 收集所有提取到的指标
                const metrics = [];
                if (icMatch) metrics.push({name: 'IC', value: parseFloat(icMatch[1]).toFixed(4)});
                if (irMatch) metrics.push({name: 'IR', value: parseFloat(irMatch[1]).toFixed(4)});
                if (sharpeMatch) metrics.push({name: 'Sharpe', value: parseFloat(sharpeMatch[1]).toFixed(4)});
                if (turnoverMatch) metrics.push({name: 'Turnover', value: parseFloat(turnoverMatch[1]).toFixed(4)});
                if (annualReturnMatch) metrics.push({name: 'Annual Return', value: `${parseFloat(annualReturnMatch[1]).toFixed(2)}%`});
                if (maxDrawdownMatch) metrics.push({name: 'Max Drawdown', value: `${parseFloat(maxDrawdownMatch[1]).toFixed(2)}%`});
                
                // 如果没有提取到任何指标，返回明确的错误信息
                if (metrics.length === 0) {
                    return '<div class="error">无法从日志中提取任何回测指标，请检查回测是否成功完成</div>';
                }
                
                // 构建HTML表格
                let html = '<table class="result-table"><thead><tr><th></th><th></th></tr></thead><tbody>';
                metrics.forEach(metric => {
                    html += `<tr><td>${metric.name}</td><td>${metric.value}</td></tr>`;
                });
                html += '</tbody></table>';
                
                // 添加警告信息如果有指标缺失
                if (metrics.length < 6) {
                    html += '<div class="warning">注意：部分回测指标缺失，可能影响结果分析</div>';
                }
                
                return html;
            } catch (error) {
                console.error('解析结果错误:', error);
                return `<div class="error">结果解析失败: ${error.message}</div>`;
            }
        }
        
        // 更新结果容器
        function updateResults(html) {
            if (typeof html === 'string') {
                resultsTableContainer.innerHTML = html;
            } else if (typeof html === 'object') {
                // 如果提供的是JSON对象，构建HTML表格
                let tableHtml = '<table class="result-table"><thead><tr><th></th><th></th></tr></thead><tbody>';
                
                for (const [key, value] of Object.entries(html)) {
                    tableHtml += `<tr><td>${key}</td><td>${value}</td></tr>`;
                }
                
                tableHtml += '</tbody></table>';
                resultsTableContainer.innerHTML = tableHtml;
            }
            
            resultsContainer.style.display = 'block';
        }
        
        // 更新进度条
        function updateProgress(percentage) {
            // 确保百分比在0-100范围内
            percentage = Math.min(100, Math.max(0, percentage));
            progressBar.style.width = `${percentage}%`;
            progressBar.textContent = `${Math.round(percentage)}%`;
        }
        
        // 设置运行状态
        function setRunningState(running) {
    isRunning = running;
    
    // 更新状态徽章
    const statusBadge = document.getElementById('statusBadge');
    if (statusBadge) {
        if (running) {
            statusBadge.textContent = '运行中';
            statusBadge.className = 'badge badge-warning';
        } else {
            // 保持原有逻辑，可根据需要调整默认状态
            statusBadge.textContent = '未运行';
            statusBadge.className = 'badge badge-secondary';
        }
    }
    
    // 更新按钮状态
    runBtn.disabled = running;
    stopBtn.disabled = !running;
    
    // 更新输入字段状态
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (input !== stopBtn) {
            input.disabled = running;
        }
    });
}
        
        // 添加日志
        function appendLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> <span class="log-message">${message}</span>`;
            logContainer.appendChild(logEntry);
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 初始化因子
        function initializeFactors() {
    // 创建第一个默认因子
    alphaFactors = [{
        id: 'factor-1',
        name: '因子 1',
        alphaExpression: '',
        neutralization: document.getElementById('neutralization-select').value,
        region: document.getElementById('region-select').value,
        universe: document.getElementById('universe-select').value,
        decay: document.getElementById('decay-input').value || '2',
        delay: document.getElementById('delay-input').value || '1',
        truncation: document.getElementById('truncation-input').value || '0.05',
        pasteurization: document.getElementById('pasteurization-select').value,
        nanHandling: document.getElementById('nan-handling-select').value,
        maxTrade: document.getElementById('max-trade-select').value,
        visualization: document.getElementById('visualization-select').value // 添加visualization属性
    }];
    
    // 渲染标签页
    renderFactorTabs();
}
        
        // 添加新因子
        function addNewFactor() {
            // 限制最多10个因子
            if (alphaFactors.length >= 10) {
                alert('最多只能添加10个因子');
                return;
            }
            
            // 保存当前因子数据
            saveCurrentFactorData();
            
            // 复制第一个因子的设置作为默认值
            const firstFactor = alphaFactors[0];
            const newFactorId = `factor-${alphaFactors.length + 1}`;
            
            // 创建新因子对象
            const newFactor = {
                id: newFactorId,
                name: `因子 ${alphaFactors.length + 1}`,
                alphaExpression: '',  // 新因子的表达式为空
                neutralization: firstFactor.neutralization,
                region: firstFactor.region,
                universe: firstFactor.universe,
                decay: firstFactor.decay,
                delay: firstFactor.delay,
                truncation: firstFactor.truncation,
                pasteurization: firstFactor.pasteurization,
                nanHandling: firstFactor.nanHandling,
                maxTrade: firstFactor.maxTrade,
                visualization: firstFactor.visualization // 继承前一个因子的visualization设置
            };
            
            // 添加到因子列表
            alphaFactors.push(newFactor);
            
            // 切换到新因子
            currentFactorIndex = alphaFactors.length - 1;
            
            // 更新UI
            renderFactorTabs();
            loadFactorData(currentFactorIndex);
        }
        
        // 删除因子
        function removeFactor(index) {
            // 至少保留一个因子
            if (alphaFactors.length <= 1) {
                alert('至少需要保留一个因子');
                return;
            }
            
            // 删除指定因子
            alphaFactors.splice(index, 1);
            
            // 更新当前因子索引
            if (currentFactorIndex >= alphaFactors.length) {
                currentFactorIndex = alphaFactors.length - 1;
            }
            
            // 更新UI
            renderFactorTabs();
            loadFactorData(currentFactorIndex);
        }
        
        // 切换因子
        function switchFactor(index) {
            // 保存当前因子数据
            saveCurrentFactorData();
            
            // 更新当前因子索引
            currentFactorIndex = index;
            
            // 加载选定因子的数据
            loadFactorData(index);
            
            // 更新UI
            renderFactorTabs();
        }
        
        // 渲染因子标签页
        function renderFactorTabs() {
            const tabsContainer = document.getElementById('factor-tabs');
            tabsContainer.innerHTML = '';
            
            // 创建每个因子的标签页
            alphaFactors.forEach((factor, index) => {
                const tab = document.createElement('div');
                tab.className = `factor-tab${index === currentFactorIndex ? ' active' : ''}`;
                tab.innerHTML = `
                    <span class="factor-tab-name">${factor.name}</span>
                    <span class="factor-tab-close" data-index="${index}">×</span>
                `;
                
                // 点击标签切换因子
                tab.addEventListener('click', (e) => {
                    // 如果点击的是关闭按钮，不切换标签
                    if (!e.target.classList.contains('factor-tab-close')) {
                        switchFactor(index);
                    }
                });
                
                tabsContainer.appendChild(tab);
            });
            
            // 为关闭按钮添加事件
            document.querySelectorAll('.factor-tab-close').forEach(closeBtn => {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    const index = parseInt(e.target.getAttribute('data-index'));
                    removeFactor(index);
                });
            });
            
            // 更新添加按钮状态
            const addFactorBtn = document.getElementById('add-factor-btn');
            addFactorBtn.disabled = alphaFactors.length >= 10;
        }
        
        // 保存当前因子数据
        function saveCurrentFactorData() {
    if (alphaFactors.length === 0) return;
    
    const currentFactor = alphaFactors[currentFactorIndex];
    currentFactor.alphaExpression = document.querySelector('.alpha-expression').value.trim();
    currentFactor.neutralization = document.getElementById('neutralization-select').value;
    currentFactor.region = document.getElementById('region-select').value;
    currentFactor.universe = document.getElementById('universe-select').value;
    currentFactor.decay = document.getElementById('decay-input').value || '2';
    currentFactor.delay = document.getElementById('delay-input').value || '1';
    currentFactor.truncation = document.getElementById('truncation-input').value || '0.05';
    currentFactor.pasteurization = document.getElementById('pasteurization-select').value;
    currentFactor.nanHandling = document.getElementById('nan-handling-select').value;
    currentFactor.maxTrade = document.getElementById('max-trade-select').value;
    currentFactor.visualization = document.getElementById('visualization-select').value; // 保存visualization设置
}
        
        // 加载因子数据到表单
        // 加载因子数据到表单
function loadFactorData(index) {
    if (alphaFactors.length === 0) return;
    
    const factor = alphaFactors[index];
    document.querySelector('.alpha-expression').value = factor.alphaExpression;
    document.getElementById('neutralization-select').value = factor.neutralization;
    document.getElementById('region-select').value = factor.region;
    
    // 更新Universe选项（区域变化会影响Universe选项）
    updateUniverseOptions();
    
    // 设置Universe值（需要在更新选项后）
    setTimeout(() => {
        document.getElementById('universe-select').value = factor.universe;
    }, 100);
    
    document.getElementById('decay-input').value = factor.decay;
    document.getElementById('delay-input').value = factor.delay;
    document.getElementById('truncation-input').value = factor.truncation;
    document.getElementById('pasteurization-select').value = factor.pasteurization;
    document.getElementById('nan-handling-select').value = factor.nanHandling;
    document.getElementById('max-trade-select').value = factor.maxTrade;
    document.getElementById('visualization-select').value = factor.visualization; // 加载visualization设置
}

    </script>
    <!-- 先加载brain.js，再加载script.js -->
    <script src="{{ url_for('static', filename='brain.js') }}"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>