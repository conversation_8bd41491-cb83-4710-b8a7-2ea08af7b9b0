import time
import datetime
import pandas as pd
import json  # 添加JSON模块导入
import os
import threading
import concurrent.futures
import requests
from requests.exceptions import RequestException, ConnectionError
import json
import argparse
import sys
import asyncio
import aiohttp  # 添加异步HTTP客户端库
# 添加：写入最新结果信息供前端访问
import json
from pathlib import Path
import datetime
import glob

brain_api_url = os.environ.get("BRAIN_API_URL", "https://api.worldquantbrain.com")

class SessionManager:
    """管理WorldQuant BRAIN API会话"""
    def __init__(self, username, password):  # 仅保留用户名密码参数
        self.session = self.create_session(username, password)
        self.start_time = time.time()
        self.lock = threading.Lock()

    def create_session(self, username, password):  # 仅保留用户名密码参数
        """使用用户名密码创建会话"""
        s = requests.Session()
        s.auth = (username, password)
        # 强制使用用户名密码登录
        login_url = "https://api.worldquantbrain.com/authentication"
        try:
            response = s.post(login_url)
            response.raise_for_status()
            print(f"用户 {username} 登录成功")
        except RequestException as e:
            print(f"登录失败: {str(e)}")
            raise  # 登录失败时终止程序
        
        print(f"用户: {username}")
        return s

# Alpha详情获取和处理函数
def locate_details(s, alpha_id):
    """获取alpha的详细信息"""
    while True:
        alpha = s.get("https://api.worldquantbrain.com/alphas/" + alpha_id)
        if "retry-after" in alpha.headers:
            time.sleep(float(alpha.headers["Retry-After"]))
        else:
            break
    string = alpha.content.decode('utf-8')
    metrics = json.loads(string)

    sharpe = metrics["is"]["sharpe"]
    fitness = metrics["is"]["fitness"]
    turnover = metrics["is"]["turnover"]
    margin = metrics["is"]["margin"]
    decay = metrics["settings"]["decay"]
    delay = metrics["settings"]["delay"]
    exp = metrics['regular']['code']
    universe = metrics["settings"]["universe"]
    truncation = metrics["settings"]["truncation"]
    neutralization = metrics["settings"]["neutralization"]
    region = metrics["settings"]["region"]
    maxTrade = metrics["settings"]["maxTrade"]
    matches_pyramid = next((check for check in metrics.get('is', {}).get('checks', []) if check.get('name') == 'MATCHES_PYRAMID'), None)
    pyramids = [p.get('name', '') for p in matches_pyramid.get('pyramids', [])] if matches_pyramid else []
    opCount = metrics['regular']['operatorCount']
    # 从is.checks中查找是否存在POWER_POOL_CORRELATION字段
    power_pool_check = next((check for check in metrics.get('is', {}).get('checks', []) if check.get('name') == 'POWER_POOL_CORRELATION'), None)
    powerpool = 'Y' if power_pool_check is not None else 'N'
    
    triple = [alpha_id, sharpe, turnover, fitness, margin, exp, region, universe, neutralization, decay, delay, truncation, maxTrade, pyramids, opCount, powerpool]
    return triple

def get_pnl(s, alpha_id):
    """获取alpha的PnL数据"""
    while True:
        pnl = s.get('https://api.worldquantbrain.com/alphas/' + alpha_id + '/recordsets/pnl')
        if pnl.headers.get('Retry-After', 0) == 0:
             break
        time.sleep(float(pnl.headers['Retry-After']))
    return pnl

def get_alpha_byid(s, alpha_id):
    """获取alpha的详细信息"""
    while True:
        alpha = s.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}")
        if "retry-after" in alpha.headers:
            time.sleep(float(alpha.headers["Retry-After"]))
        else:
            break
    return json.loads(alpha.content.decode('utf-8'))

async def simulate_alpha_new(session, alpha_config):
    """异步模拟alpha"""
    MAX_RETRIES = 3
    retry_count = 0
    print(f"[DEBUG] 开始模拟单个alpha: {alpha_config.get('regular', '')[:30]}...", flush=True)

    while retry_count < MAX_RETRIES:
        try:
            print(f"[DEBUG] 提交API请求 (第 {retry_count+1}/{MAX_RETRIES} 次尝试)", flush=True)
            print(f"[DEBUG] 请求参数: {json.dumps(alpha_config, ensure_ascii=False)[:200]}...", flush=True)
            async with session.post('https://api.worldquantbrain.com/simulations', 
                                   json=alpha_config) as response:
                print(f"[DEBUG] API响应状态码: {response.status}", flush=True)
                if response.status == 200 or response.status == 201:
                    result = await response.json()
                    print(f"[DEBUG] 模拟成功，获取到alpha_id: {result.get('id')}", flush=True)
                    return result.get('id')
                elif response.status == 429:  # Rate limit
                    retry_after = int(response.headers.get('Retry-After', 5))
                    print(f"Rate limit reached, waiting for {retry_after} seconds...", flush=True)
                    await asyncio.sleep(retry_after)
                    retry_count += 1
                    continue
                else:
                    # 新增：解析JSON响应并添加类型检查
                    try:
                        json_data = await response.json()
                        # 处理列表类型响应
                        if isinstance(json_data, list):
                            if json_data and isinstance(json_data[0], dict):
                                detail = json_data[0].get("detail", 0)
                            else:
                                detail = json_data[0] if json_data else 0
                        else:
                            # 处理字典类型响应
                            if isinstance(json_data, dict):
                                detail = json_data.get("detail", 0)
                            else:
                                detail = str(json_data)
                        print(f"API错误详情: {detail}", flush=True)
                    except:
                        resp_text = await response.text()
                        print(f"提交模拟alpha失败: {response.status} - {resp_text}", flush=True)
                    return None
        except Exception as e:
            print(f"提交模拟alpha时出错: {str(e)}", flush=True)
            retry_count += 1
            await asyncio.sleep(5)
    
    print(f"达到最大重试次数，无法提交模拟alpha", flush=True)
    return None

# 在现有函数上方添加一个新函数，用于并发处理多个alpha
async def simulate_alpha_batch_concurrent(session_manager, alpha_configs):
    """一次性提交并发模拟多个alphas"""
    results = []
    
    try:
        # 创建异步session并复制cookies
        async with aiohttp.ClientSession(cookies=session_manager.session.cookies.get_dict()) as session:
            print(f"一次性提交{len(alpha_configs)}个alpha进行并发模拟", flush=True)
            
            # 一次性提交所有alpha
            async with session.post('https://api.worldquantbrain.com/simulations', 
                                   json=alpha_configs, timeout=60) as resp:
                if resp.status < 200 or resp.status >= 300:
                    error_text = await resp.text()
                    print(f"提交多个alpha失败: 状态码 {resp.status}, 错误: {error_text}", flush=True)
                    return []
                    
                simulation_progress_url = resp.headers.get('Location')
                if not simulation_progress_url:
                    error_text = await resp.text()
                    print(f"未获取到模拟进度URL: {error_text}", flush=True)
                    return []
            
            # 检查进度，最多等待5分钟
            start_time = time.time()
            children = []
            while True:
                # 超时检查
                if time.time() - start_time > 600:
                    print(f"模拟进度检查超时(10分钟), progress_url: {simulation_progress_url} ", flush=True)
                    return results
                    
                try:
                    async with session.get(simulation_progress_url) as resps:
                        json_data = await resps.json()
                        children = json_data.get("children", [])
                        
                        # 获取响应头的Retry-After
                        retry_after = resps.headers.get('Retry-After')
                        if not retry_after:
                            status = json_data.get("status")
                            if status == 'ERROR':
                                print(f"模拟出错: {simulation_progress_url}")
                                break
                            elif status != "COMPLETE":
                                print(f"模拟未完成: {simulation_progress_url}", flush=True)
                                # 尝试删除未完成的模拟
                                async with session.delete(simulation_progress_url) as delete_resp:
                                    delete_json = await delete_resp.json()
                                    detail = delete_json.get("detail")
                                    if detail == "未找到。":
                                        print(f"成功删除: {simulation_progress_url}", flush=True)
                                    else:
                                        print(f"删除失败: {simulation_progress_url}", flush=True)
                                break
                            else:
                                print(f'模拟完成: {simulation_progress_url}', flush=True)
                                break
                        # 等待指定的重试时间
                        await asyncio.sleep(float(retry_after) if retry_after else 5)
                except Exception as e:
                    print(f"检查模拟进度时出错: {str(e)}")
                    await asyncio.sleep(5)
            
            # 处理每个子模拟的结果
            for i, child in enumerate(children):
                try:
                    async with session.get(f"https://api.worldquantbrain.com/simulations/{child}") as child_resp:
                        if child_resp.status < 200 or child_resp.status >= 300:
                            print(f"获取子模拟结果失败: {child}, 状态码: {child_resp.status}")
                            continue
                            
                        child_data = await child_resp.json()
                        alpha_id = child_data.get("alpha")
                        if not alpha_id:
                            print(f"子模拟结果中未找到alpha_id: {child}")
                            continue
                        
                        # 获取alpha详情
                        async with session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}") as alpha_resp:
                            if alpha_resp.status < 200 or alpha_resp.status >= 300:
                                print(f"获取alpha详情失败: {alpha_id}, 状态码: {alpha_resp.status}")
                                continue
                                
                            alpha_result = await alpha_resp.json()
                            
                            # 设置alpha_backtester标签
                            try:
                                params = {"tags": ["alpha_backtester"]}
                                async with session.patch(f'https://api.worldquantbrain.com/alphas/{alpha_id}', 
                                                        json=params, timeout=60) as tag_resp:
                                    if 200 <= tag_resp.status < 300:
                                        print(f"成功为alpha设置标签: {alpha_id}, 标签: alpha_backtester")
                                    else:
                                        print(f"设置alpha标签失败: {tag_resp.status} - {await tag_resp.text()}")
                            except Exception as e:
                                print(f"设置alpha标签时出错: {str(e)}")
                            
                            matches_pyramid = next((check for check in alpha_result.get('is', {}).get('checks', []) if check.get('name') == 'MATCHES_PYRAMID'), None)
                            pyramids = [p.get('name', '') for p in matches_pyramid.get('pyramids', [])] if matches_pyramid else []
    
                            # 提取并保存结果
                            result_data = {
                                'alpha_id': alpha_id,
                                'sharpe': alpha_result.get('is', {}).get('sharpe'),
                                'turnover': alpha_result.get('is', {}).get('turnover'),
                                'fitness': alpha_result.get('is', {}).get('fitness'),
                                'margin': alpha_result.get('is', {}).get('margin'),
                                'powerpool': alpha_result.get('is', {}).get('powerpool', False),
                                'opCount': alpha_result.get('metrics', {}).get('opCount', 0),
                                'pyramids': pyramids,
                                'expression': alpha_configs[i].get('regular') if alpha_configs[i].get('type') == 'REGULAR' else ''                                
                            }
                            results.append(result_data)
                except Exception as e:
                    print(f"处理子模拟结果时出错: {str(e)}")
    except Exception as e:
        print(f"并发模拟alpha时出错: {str(e)}")
    
    return results

# 修改现有的simulate_alpha函数，添加标签功能
async def simulate_alpha(session_manager, alpha_config):
    """异步模拟alpha"""
    try:
        # 创建异步session并复制cookies
        async with aiohttp.ClientSession(cookies=session_manager.session.cookies.get_dict()) as session:
            # 提交alpha
            alpha_id = await simulate_alpha_new(session, alpha_config)
            if not alpha_id:
                return None
            
            # 等待并检查alpha状态
            alpha_result = await locate_details(session, alpha_id)
            if not alpha_result:
                return None

            matches_pyramid = next((check for check in alpha_result.get('is', {}).get('checks', []) if check.get('name') == 'MATCHES_PYRAMID'), None)
            pyramids = [p.get('name', '') for p in matches_pyramid.get('pyramids', [])] if matches_pyramid else []

            # 提取结果
            result_data = {
                'alpha_id': alpha_id,
                'sharpe': alpha_result.get('is', {}).get('sharpe'),
                'turnover': alpha_result.get('is', {}).get('turnover'),
                'fitness': alpha_result.get('is', {}).get('fitness'),
                'margin': alpha_result.get('is', {}).get('margin'),
                'powerpool': alpha_result.get('is', {}).get('powerpool', False),
                'opCount': alpha_result.get('metrics', {}).get('opCount', 0),
                'pyramids': pyramids,
                'expression': alpha_config.get('regular') if alpha_config.get('type') == 'REGULAR' else ''  
            }
            
            # 设置alpha_backtester标签
            try:
                params = {"tags": ["alpha_backtester"]}
                async with session.patch(f'https://api.worldquantbrain.com/alphas/{alpha_id}', json=params, timeout=30) as response:
                    if 200 <= response.status < 300:
                        print(f"成功为alpha设置标签: {alpha_id}, 标签: alpha_backtester")
                    else:
                        print(f"设置alpha标签失败: {response.status} - {await response.text()}")
            except Exception as e:
                print(f"设置alpha标签时出错: {str(e)}")
            
            return result_data
    except Exception as e:
        print(f"模拟alpha时出错: {str(e)}")
        return None

# 修改simulate_alpha_batch函数，根据批次大小决定使用哪种方式
async def simulate_alpha_batch(session_manager, alpha_configs, batch_size=10):
    """异步批量模拟alphas"""
    results = []
    
    # 如果只有一个alpha，使用单个模拟方式
    if len(alpha_configs) == 1:
        result = await simulate_alpha(session_manager, alpha_configs[0])
        if result:
            results.append(result)
        return results
    
    # 分批处理
    for i in range(0, len(alpha_configs), batch_size):
        batch = alpha_configs[i:i+batch_size]
        print(f"处理批次 {i//batch_size + 1}/{(len(alpha_configs) + batch_size - 1)//batch_size}, {len(batch)}个表达式", flush=True)
        
        # 使用并发方式一次性提交一批alpha
        batch_results = await simulate_alpha_batch_concurrent(session_manager, batch)
        results.extend(batch_results)
        
        print(f"批次 {i//batch_size + 1} 完成，成功: {len(batch_results)}/{len(batch)}", flush=True)
    
    return results

# 保留第一个实现并添加API功能

# 只保留第一个实现并修复变量作用域问题
def simulate_multis(session_manager, alphas, name, tags):
    results = []
    if not alphas:
        return results
    print(f"[DEBUG] 开始模拟多个alpha: 数量={len(alphas)}, 名称={name}", flush=True)
    try:
        # 处理多个alpha的API调用逻辑
        if len(alphas) > 1:
            # 添加API调用和重试机制
            result_ids = []
            while True:
                try:
                    print(f"[DEBUG] 提交多个alpha API请求", flush=True)
                    print(f"[DEBUG] 请求参数数量: {len(alphas)} 个alpha", flush=True)
                    resp = session_manager.session.post('https://api.worldquantbrain.com/simulations',
                                                        json=alphas)
                    simulation_progress_url = resp.headers.get('Location', 0)
                    if simulation_progress_url == 0:
                        # 处理API错误响应
                        json_data = resp.json()
                        if isinstance(json_data, list):
                            if json_data and isinstance(json_data[0], dict):
                                detail = json_data[0].get("detail", 0)
                            else:
                                detail = json_data[0] if json_data else 0
                        else:
                            # 新增类型检查，避免列表误判
                            if isinstance(json_data, dict):
                                detail = json_data.get("detail", 0)
                            else:
                                detail = json_data[0] if isinstance(json_data, list) and json_data else 0
                        if detail in ['SIMULATION_LIMIT_EXCEEDED', 'CONCURRENT_SIMULATION_LIMIT_EXCEEDED']:
                            print(f"卡槽已满: {detail}", flush=True)
                            time.sleep(1)
                        else:
                            print(f"API错误: {detail}", flush=True)
                            return result_ids
                    else:
                        print('simulation_progress_url:', simulation_progress_url, flush=True)
                        break
                except Exception as e:
                    print(f"API调用错误: {str(e)}", flush=True)
                    time.sleep(30)

            # 添加进度检查逻辑
            get_start_time = time.time()
            while True:
                if time.time() - get_start_time > 600:
                    print("模拟进度检查超时", flush=True)
                    return result_ids
                try:
                    resps = session_manager.session.get(simulation_progress_url)
                    json_data = resps.json()
                    children = json_data.get("children", [])
                    headers = resps.headers
                    retry_after = headers.get('Retry-After', 0)
                    if retry_after == 0:
                        status = json_data.get("status", 0)
                        if status == 'COMPLETE':
                            print('模拟完成', flush=True)
                            break
                        else:
                            print(f"模拟状态: {status}", flush=True)
                            time.sleep(1)
                    time.sleep(float(retry_after))
                except Exception as e:
                    print(f"进度检查错误: {str(e)}", flush=True)
                    time.sleep(30)

            # 处理结果
            for alpha, child in zip(alphas, children):
                try:
                    brain_api_url = "https://api.worldquantbrain.com"
                    child_progress = session_manager.session.get(f"{brain_api_url}/simulations/{child}")
                    json_data = child_progress.json()
                    alpha_id = json_data["alpha"]
                    set_alpha_properties(session_manager.session, alpha_id, name=name, tags=tags)
                    result_ids.append(alpha_id)
                except Exception as e:
                    print(f"处理子任务错误: {str(e)}", flush=True)
            return result_ids

        # 处理单个alpha的情况
        else:
            simulation_data = alphas[0]
            # 单个alpha的API调用逻辑
            result_ids = []
            while True:
                try:
                    resp = session_manager.session.post('https://api.worldquantbrain.com/simulations',
                                                        json=simulation_data)
                    simulation_progress_url = resp.headers.get('Location', 0)
                    if simulation_progress_url == 0:
                        json_data = resp.json()
                        if isinstance(json_data, list):
                            detail = json_data[0].get("detail", 0) if json_data else 0
                        else:
                            detail = json_data.get("detail", 0)
                        if detail in ['SIMULATION_LIMIT_EXCEEDED', 'CONCURRENT_SIMULATION_LIMIT_EXCEEDED']:
                            print(f"卡槽已满: {detail}", flush=True)
                            time.sleep(1)
                        else:
                            print(f"API错误: {detail}", flush=True)
                            return result_ids
                    else:
                        print('simulation_progress_url:', simulation_progress_url, flush=True)
                        break
                except Exception as e:
                    print(f"API调用错误: {str(e)}", flush=True)
                    time.sleep(60)

            # 检查进度阶段
            get_start_time = time.time()
            while True:
                if time.time() - get_start_time > 600:
                    print("模拟进度检查超时", flush=True)
                    return result_ids
                try:
                    resp = session_manager.session.get(simulation_progress_url)
                    json_data = resp.json()
                    headers = resp.headers
                    retry_after = headers.get('Retry-After', 0)
                    if retry_after == 0:
                        break
                    time.sleep(float(retry_after))
                except Exception as e:
                    print(f"进度检查错误: {str(e)}", flush=True)
                    time.sleep(60)

            # 处理结果
            try:
                alpha_id = json_data.get("alpha")
                set_alpha_properties(session_manager.session, alpha_id, name=name, tags=tags)
                result_ids.append(alpha_id)
            except Exception as e:
                print(f"处理结果错误: {str(e)}", flush=True)

            if alpha_id:
                set_alpha_properties(session_manager.session, alpha_id, tags=tags)
                [alpha_id, sharpe, turnover, fitness, margin, exp, region, universe, neutralization, decay, delay, truncation, maxTrade, pyramids, opCount, powerpool] = locate_details(session_manager.session, alpha_id)
                results.append({
                    'alpha_id': alpha_id,
                    'expression': exp,
                    'metrics': {
                        'sharpe': sharpe,
                        'turnover': turnover,
                        'fitness': fitness,
                        'margin': margin,
                        'powerpool': powerpool
                    }
                })
            
            # 确保返回 result_ids 而不是 results
            return result_ids

    except Exception as e:
        print(f"simulate_multis 出错: {str(e)}", flush=True)
    return results

def simulate_multiple_alphas_with_retry(session_manager, alpha_list, name="alpha_backtester", n_jobs=1, max_retries=5):
    """
    包装simulate_multiple_alphas函数，提供自动重试功能
    当结果列表长度等于初始alpha_list长度或重试次数达到上限时退出
    每次只处理尚未完成的alpha，并从文件中读取已完成的alpha列表
    """
    original_alpha_count = len(alpha_list)
    all_results = []
    retries = 0
    
    # 创建用于存储结果的文件路径
    result_file_path = f'records/{name}_simulated_alpha_expression.txt'

    while retries < max_retries:
        # 从文件中读取已完成的alpha表达式
        completed_alphas = set()
        try:
            with open(result_file_path, mode='r') as f:
                for line in f:
                    completed_alphas.add(line.strip())
            print(f"从文件中读取到{len(completed_alphas)}个已完成的alpha", flush=True)
        except FileNotFoundError:
            print(f"文件{result_file_path}不存在，请创建新文件", flush=True)
            os.makedirs('records', exist_ok=True)
        
        # 过滤出尚未完成的alpha
        remaining_alphas = []
        for alpha in alpha_list:
            # 生成唯一标识：组合 alpha['regular'] 和 alpha['settings']（使用稳定的JSON序列化）
            settings_str = json.dumps(alpha['settings'], sort_keys=True)  # 确保字典序列化一致
            regular_str = json.dumps(alpha['regular'], sort_keys=True)  # 将regular字典转换为字符串
            unique_id = f"{regular_str}|{settings_str}"
            
            # 检查是否有任何已完成记录包含当前unique_id
            if not any(unique_id in line for line in completed_alphas):
                remaining_alphas.append(alpha)

            for line in completed_alphas:
                if "|" in line and unique_id in line:
                    alpha_id = line.split("|")[0]
                    if alpha_id not in all_results:
                        all_results.append(alpha_id)
        
        # 如果所有alpha都已完成，提前退出
        if not remaining_alphas:
            print(f"所有{original_alpha_count}个alpha已完成，无需继续重试", flush=True)
            break
            
        print(f"第{retries+1}次尝试，处理{len(remaining_alphas)}/{original_alpha_count}个未完成的alpha", flush=True)
        
        # 调用原始函数处理剩余的alpha
        if remaining_alphas:
            result_ids = simulate_multiple_alphas(session_manager, remaining_alphas, name, n_jobs)
            if result_ids:
                all_results.extend(result_ids)
                if len(all_results) >= original_alpha_count:
                    print(f"所有{original_alpha_count}个alpha已完成，提前退出重试循环", flush=True)
                    break
        retries += 1
        if retries < max_retries and remaining_alphas:
            print(f"准备第{retries+1}次重试，还剩{len(remaining_alphas)}个未完成的alpha", flush=True)
            time.sleep(5)  # 在重试前短暂等待
    
    if retries == max_retries and remaining_alphas:
        print(f"已达到最大重试次数{max_retries}，仍有{len(remaining_alphas)}/{original_alpha_count}个alpha未完成", flush=True)
    
    return all_results

def set_alpha_properties(
        s,
        alpha_id,
        name: str = None,
        color: str = None,
        selection_desc: str = None,
        combo_desc: str = None,
        tags: list = None,  # ['tag1', 'tag2']
):
    """
    Function changes alpha's description parameters (with 3 retries)
    """

    if alpha_id is None:
        print("Alpha ID 为空，无法进行属性更新。", flush=True)
        return False
    max_retries = 3
    params = {
        "category": None,
        "regular": {"description": None},
    }
    if color:
        params["color"] = color
    if name:
        params["name"] = name
    if tags:
        params["tags"] = tags
    if combo_desc:
        params["combo"] = {"description": combo_desc}
    if selection_desc:
        params["selection"] = {"description": selection_desc}

    for retry in range(max_retries):
        try:
            response = s.patch(
                "https://api.worldquantbrain.com/alphas/" + alpha_id, json=params
            )
            # 检查响应状态码是否为成功（2xx）
            if 200 <= response.status_code < 300:
                print(f"成功设置 alpha_id: {alpha_id}, 标签: {tags if tags else '无'}（第 {retry + 1}/{max_retries} 次尝试）", flush=True)
                return response  # 成功则返回响应
            else:
                if response.status_code == 429:
                    print(f"请求过多（429），尝试重新登录...（第 {retry + 1}/{max_retries} 次尝试）",flush = True)
                print(f"请求失败（状态码: {response.status_code}）alpha_id: {alpha_id}（第 {retry + 1}/{max_retries} 次尝试）",flush=True)
        except Exception as e:
            print(f"请求异常: {str(e)}，alpha_id: {alpha_id}（第 {retry + 1}/{max_retries} 次尝试）",flush=True)
        
        # 非最后一次重试时等待1秒
        if retry < max_retries - 1:
            time.sleep(1)

    # 所有重试均失败
    print(f"三次重试均失败，alpha_id: {alpha_id}", flush=True)
    return None


file_lock = threading.Lock()
def write_to_file(alpha, name):
    with file_lock:
        try:
            os.makedirs('records', exist_ok=True)
            with open(f'records/{name}_simulated_alpha_expression.txt', mode='a') as f:
                f.write(alpha + '\n')
                f.flush()
                print(f"Alpha expression written to file: {alpha}", flush=True)
        except Exception as e:
            print(f"写入文件时出错: {e}", flush=True)


def to_multi_alphas(alpha_list, batch_size=10):
    return [alpha_list[i:i + batch_size] for i in range(0, len(alpha_list), batch_size)]


def simulate_multiple_alphas(session_manager, alpha_list, name = "alpha_backtester", n_jobs=1):
    tags=[name]

    chunk_size = (len(alpha_list) + n_jobs - 1) // n_jobs
    task_chunks = [alpha_list[i:i + chunk_size] for i in range(0, len(alpha_list), chunk_size)]
    total_tasks = len(alpha_list)
    start_time = time.time()
    completed_count = 0
    count_lock = threading.Lock()
    all_results = []

    def wrap_task(session_manager, alphas, name, tags):
        nonlocal completed_count
        try:
            # 调用同步函数，接收返回的列表
            result_list = simulate_multis(session_manager, alphas, name, tags)
            with count_lock:  # 使用锁保护共享数据
                return result_list
        except Exception as e:
            print(f"任务失败：alpha={alphas}, 错误={type(e).__name__}-{str(e)}", flush=True)  # 记录具体错误
            return []
        finally:
            with count_lock:
                completed_count += len(alphas)
                elapsed_time = time.time() - start_time
                average_time = elapsed_time / completed_count if completed_count > 0 else 0
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time}] 完成了总数 {total_tasks} 中的第 {completed_count} 个任务，平均耗时: {average_time:.2f} 秒", flush=True)

    # 使用线程池执行任务
    with concurrent.futures.ThreadPoolExecutor(max_workers=n_jobs) as executor:
        futures = []
        for task_chunk in task_chunks:
            # 获取当前 chunk 对应的 session_manager
            current_session_manager = session_manager
            multi_alphas = to_multi_alphas(task_chunk, 10) 
            for alphas in multi_alphas:
                # 将任务与当前的 session_manager 关联
                future = executor.submit(wrap_task, current_session_manager, alphas, name, tags)
                futures.append(future)
        
        # 使用as_completed可以在任务完成时立即获取结果
        for future in concurrent.futures.as_completed(futures, timeout=6*60*60):
            result = future.result()
            if result:
                all_results.extend(result)
        return all_results

# 修改saveResults函数，添加输出结果文件路径的代码
def saveResults(results, output_file):
    """保存回测结果到CSV文件"""
    if not results or len(results) == 0:
        print("没有有效的回测结果", flush=True)
        return
        
    # 创建结果DataFrame
    df_list = []
    for result in results:
        if not result or not isinstance(result, dict):
            continue
            
        # 从结果字典中提取数据
        alpha_id = result.get('alpha_id', 'unknown')
        expression = result.get('expression', '')
        metrics = result.get('metrics', {})
        
        # 创建结果行
        result_row = {
            'alpha_id': alpha_id,
            'expression': expression
        }
        
        # 添加所有指标
        for key, value in metrics.items():
            result_row[key] = value
            
        df_list.append(result_row)
    
    # 如果有有效结果，保存到CSV
    if df_list:
        import pandas as pd
        df = pd.DataFrame(df_list)
        df.to_csv(output_file, index=False)
        print(f"结果已保存到: {output_file}", flush=True)
        return True
    else:
        print("没有结果可以保存", flush=True)
        return False

# 继续完成run_backtest_with_session函数
# 修正函数参数以匹配调用位置
# 移除brain_session_id和params参数，添加实际传入的参数
def run_backtest_with_session(alphas_file, username, password, output_file=None):
    # 添加：清除旧结果文件
    latest_result_path = os.path.join('static', 'latest_result.json')
    if os.path.exists(latest_result_path):
        os.remove(latest_result_path)
        print("已清除旧结果文件")
    
    print(f"[DEBUG] 开始执行回测: 文件={alphas_file}, 输出={output_file}", flush=True)
    # 调用回测执行函数
    return run_backtest(alphas_file, username, password, output_file)

# 独立定义回测执行函数
def run_backtest(alphas_file, username, password, output_file=None):
    """使用用户名密码运行回测"""
    print(f"开始使用用户 {username} 进行回测", flush=True)
    print(f"[DEBUG] 回测参数: 用户名={username}, 文件={alphas_file}, 输出={output_file}", flush=True)

    # 创建会话管理器（仅传递用户名密码）
    print(f"[DEBUG] 创建会话管理器", flush=True)
    session_manager = SessionManager(username, password)
    
    alpha_list = []
    try:
        with open(alphas_file, 'r') as f:
            # 从JSON文件加载alpha表达式列表
            print(f"[DEBUG] 读取alpha文件: {alphas_file}", flush=True)
            alpha_expressions = json.load(f)
            print(f"[DEBUG] 加载到 {len(alpha_expressions)} 个表达式", flush=True)
            alpha_list = alpha_expressions  # 添加这一行
        print(f"👨‍💻 从文件 {alphas_file} 中读取了 {len(alpha_list)} 个alpha表达式", flush=True)
        
        try:
            results = simulate_multiple_alphas_with_retry(session_manager, alpha_list, name="alpha_backtester", n_jobs=1, max_retries=5)
            print(f"总共模拟了 {len(results)}/{len(alpha_list)} 个alphas", flush=True)

            new_results = []
            for alpha_id in results:
                [alpha_id, sharpe, turnover, fitness, margin, exp, region, universe, neutralization, decay, delay, truncation, maxTrade, pyramids, opCount, powerpool]=locate_details(session_manager.session, alpha_id)

                # 提取并保存结果
                result_data = {
                    'alpha_id': alpha_id,
                    'sharpe': sharpe,
                    'turnover': turnover,
                    'fitness': fitness,
                    'margin': margin,
                    'powerpool': powerpool,
                    'opCount': opCount,
                    'pyramids': pyramids,
                    'expression': exp                                
                }
                new_results.append(result_data)
            
            # 确保result_dir在所有代码路径中都已定义
            result_dir = os.path.join(os.path.dirname(__file__), 'static', 'backtest_results')
            os.makedirs(result_dir, exist_ok=True)
            
            if output_file:
                # 提取文件名并强制保存到static/backtest_results目录
                filename = os.path.basename(output_file)
                output_path = os.path.join(result_dir, filename)
            else:
                # 使用默认文件名并保存到static目录
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(result_dir, f"backtest_results_{timestamp}.csv")
            
            # 直接将new_results转换为DataFrame，保留所有字段
            df = pd.DataFrame(new_results)
            # 显式指定所有列，确保顺序和完整性
            columns = ['alpha_id', 'sharpe', 'turnover', 'fitness', 'margin', 
                      'powerpool', 'opCount', 'pyramids', 'expression']
            # 确保所有列都存在，缺失列填充NaN
            for col in columns:
                if col not in df.columns:
                    df[col] = None
            # 按指定顺序保存
            df[columns].to_csv(output_path, index=False)
            print(f"结果已保存到: {output_path}", flush=True)

            # 构建前端可访问的URL路径
            static_dir = Path(__file__).parent / "static"
            static_dir.mkdir(exist_ok=True)
            output_abs_path = Path(output_path).resolve()
            relative_path = output_abs_path.relative_to(static_dir.resolve())
            url_path = f"/static/{relative_path}"

            # 保存结果元信息
            latest_result = {
                "file_path": url_path,
                "timestamp": datetime.datetime.now().isoformat(),
                "results_count": len(new_results)
            }
            with open(static_dir / "latest_result.json", "w") as f:
                json.dump(latest_result, f)

            def cleanup_old_results(max_keep=5):
                result_dir = static_dir / "backtest_results"
                if not result_dir.exists():
                    return

                # 获取所有结果文件并按修改时间排序（最新的在后）
                result_files = sorted(
                    glob.glob(str(result_dir / "backtest_results_*.csv")),
                    key=os.path.getmtime
                )

                # 如果文件数量超过max_keep，删除最旧的
                if len(result_files) > max_keep:
                    files_to_delete = result_files[:-max_keep]
                    for file_path in files_to_delete:
                        try:
                            os.remove(file_path)
                            print(f"已清理旧结果文件: {os.path.basename(file_path)}", flush=True)
                        except Exception as e:
                            print(f"清理文件 {file_path} 失败: {str(e)}", flush=True)

            # 新增：清理records目录下的临时文件
            def cleanup_temp_records(name="alpha_backtester"):
                records_dir = os.path.join(os.path.dirname(__file__), 'records')
                if not os.path.exists(records_dir):
                    return
                
                # 匹配临时记录文件模式
                temp_files = glob.glob(os.path.join(records_dir, f"{name}_simulated_alpha_expression.txt"))
                
                for file_path in temp_files:
                    try:
                        os.remove(file_path)
                        print(f"已删除临时记录文件: {os.path.basename(file_path)}", flush=True)
                    except Exception as e:
                        print(f"删除临时文件 {file_path} 失败: {str(e)}", flush=True)
            
            # 调用清理函数
            cleanup_temp_records()

            return new_results
        
        except Exception as e:
            print(f"模拟多个alphas时出错: {str(e)}")
            return None

    except FileNotFoundError:
        print(f"错误：文件 {alphas_file} 不存在")
    except json.JSONDecodeError:
        print(f"错误：文件 {alphas_file} 不是有效的JSON格式")
    except Exception as e:
        print(f"处理文件时出错：{str(e)}")
    
    return None

# 添加主函数入口点
if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='使用用户名密码运行alpha回测')
    parser.add_argument('-f', '--file', required=True, help='包含alpha列表的JSON文件路径')
    parser.add_argument('-u', '--username', required=True, help='BRAIN用户名')  # 设置为必填项
    parser.add_argument('-p', '--password', required=True, help='BRAIN密码')  # 设置为必填项
    parser.add_argument('-o', '--output', help='输出结果文件路径')
    
    # 将导入语句中的文件名更新
    from appbacktester import app, some_function
    args = parser.parse_args()
    
    if not args.file or not args.username or not args.password:
        print("错误：必须提供文件路径、用户名和密码", flush=True)
        sys.exit(1)
    
    # 运行回测（移除session_id参数）
    results = run_backtest_with_session(
        args.file, 
        username=args.username, 
        password=args.password,  # 传递密码
        output_file=args.output
    )
    
    if results:
        print("回测完成，结果已保存", flush=True)
        sys.exit(0)
    else:
        print("回测失败或未返回结果", flush=True)
        sys.exit(1)