/**
 * BRAIN API Integration Module
 * Handles authentication, operators, and data fields from WorldQuant BRAIN
 * Now uses a local Python proxy server to bypass CORS restrictions
 */

// BRAIN session and data storage
let brainSession = null;
let brainOperators = null;
let brainDataFields = null;
let brainSessionId = localStorage.getItem('brain_session_id');

// Flask app API endpoints
const PROXY_BASE = '';

// Open BRAIN login modal
function openBrainLoginModal() {
    const modal = document.getElementById('brainLoginModal');
    const statusDiv = document.getElementById('brainLoginStatus');
    statusDiv.innerHTML = '';
    statusDiv.className = 'login-status';
    
    // Clear previous inputs
    document.getElementById('brainUsername').value = '';
    document.getElementById('brainPassword').value = '';
    
    modal.style.display = 'block';
    document.getElementById('brainUsername').focus();
}

// Close BRAIN login modal
function closeBrainLoginModal() {
    const modal = document.getElementById('brainLoginModal');
    const loginBtn = document.getElementById('loginBtn');
    
    // Don't allow closing if login is in progress
    if (loginBtn.disabled) {
        return;
    }
    
    modal.style.display = 'none';
}

// Get user operators via proxy server
// 删除 brainOperators 变量
// let brainOperators = null;
// async function getUserOperators() {
//     if (!brainSession || !brainSessionId) {
//         throw new Error('Not authenticated with BRAIN');
//     }
//     
//     try {
//         const response = await fetch(`${PROXY_BASE}/api/operators`, {
//             method: 'GET',
//             headers: {
//                 'Session-ID': brainSessionId
//             }
//         });
// 
//         if (!response.ok) {
//             const errorData = await response.json();
//             throw new Error(errorData.error || 'Failed to fetch operators');
//         }
//         
//         const operators = await response.json();
//         console.log(`Received ${operators.length} operators from BRAIN API`);
//         
//         // Log the categories to verify we have all operator types
//         const categories = [...new Set(operators.map(op => op.category))].sort();
//         console.log(`Operator categories: ${categories.join(', ')}`);
//         
//         return operators;
//         
//     } catch (error) {
//         console.error('Failed to fetch operators:', error);
//         throw error;
//     }
// }

// Get data fields via proxy server
async function getDataFields(region = 'USA', delay = 1, universe = 'TOP3000', datasetId = 'fundamental6') {
    if (!brainSession || !brainSessionId) {
        throw new Error('Not authenticated with BRAIN');
    }
    
    try {
        const params = new URLSearchParams({
            region: region,
            delay: delay.toString(),
            universe: universe,
            dataset_id: datasetId
        });
        
        const response = await fetch(`${PROXY_BASE}/api/datafields?${params}`, {
            method: 'GET',
            headers: {
                'Session-ID': brainSessionId
            }
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to fetch data fields');
        }
        
        return await response.json();
        
    } catch (error) {
        console.error('Failed to fetch data fields:', error);
        throw error;
    }
}

// Authenticate with BRAIN via proxy server
async function authenticateBrain() {
    const username = document.getElementById('brainUsername').value.trim();
    const password = document.getElementById('brainPassword').value;
    const statusDiv = document.getElementById('brainLoginStatus');
    const loginBtn = document.getElementById('loginBtn');
    const spinner = document.getElementById('loginSpinner');
    const modal = document.getElementById('brainLoginModal');
    
    if (!username || !password) {
        showLoginStatus('请输入用户名和密码。', 'error');
        return;
    }
    
    // Disable all inputs and buttons
    document.getElementById('brainUsername').disabled = true;
    document.getElementById('brainPassword').disabled = true;
    document.getElementById('cancelBtn').disabled = true;
    loginBtn.disabled = true;
    loginBtn.textContent = 'Connecting...';
    
    // Show spinner
    spinner.style.display = 'block';
    
    // Disable modal closing
    modal.querySelector('.close').style.display = 'none';
    
    // Show loading state
    showLoginStatus('正在连接代理服务器...', 'loading');
    
    try {
        showLoginStatus('正在BRAIN认证...', 'loading');
        
        // Authenticate via proxy server
        const authResponse = await fetch(`${PROXY_BASE}/api/authenticate`, {
            method: 'POST',
            credentials: 'include', // 添加credentials选项以处理cookies
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: username,
                password: password
            })
        });
        
        if (!authResponse.ok) {
            const errorData = await authResponse.json();
            throw new Error(errorData.error || '认证失败');
        }
        
        localStorage.setItem('username', username);
        localStorage.setItem('password', password);
        
        // 恢复认证响应处理
        const authData = await authResponse.json();
        // 修改：同时存储构造的session_id和真实session_id
        brainSessionId = authData.session_id;
        localStorage.setItem('brain_session_id', brainSessionId);
        
        // 获取用户信息
        const userResponse = await fetch(`${PROXY_BASE}/api/userinfo`, {
            method: 'GET',
            headers: {
                'Session-ID': brainSessionId,
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        });
        
        if (!userResponse.ok) {
            throw new Error('获取用户信息失败');
        }
        
        const userData = await userResponse.json();
        brainSession = { authenticated: true, username: userData.username };
        // 删除此行 -> brainOperators = await getUserOperators();
        
        // Update UI
        updateConnectedState();
        // 在认证成功后直接关闭模态框，绕过按钮状态检查
        // closeBrainLoginModal(); // 替换这行
        const modal = document.getElementById('brainLoginModal');
        modal.style.display = 'none';
        
    } catch (error) {
        showLoginStatus(error.message, 'error');
        console.error('Authentication error:', error);
    } finally {
        // Re-enable inputs and buttons
        document.getElementById('brainUsername').disabled = false;
        document.getElementById('brainPassword').disabled = false;
        document.getElementById('cancelBtn').disabled = false;
        loginBtn.disabled = false;
        loginBtn.textContent = '登录BRAIN';
        
        // Hide spinner
        spinner.style.display = 'none';
        
        // Re-enable modal closing
        modal.querySelector('.close').style.display = 'block';
    }
}

// Update UI to show connected state
function updateConnectedState() {
    const connectBtn = document.getElementById('connectToBrain');
    connectBtn.textContent = '已登录';
    connectBtn.className = 'btn btn-brain connected';
    
    // 启用Alpha Backtester按钮
    const alphaBacktesterBtn = document.getElementById('alphaBacktesterBtn');
    if (alphaBacktesterBtn) {
        alphaBacktesterBtn.disabled = false;
    }
    
    // Show connection info in the grammar errors area
    const errorsDiv = document.getElementById('grammarErrors');
    if (errorsDiv) { // 添加元素存在性检查
        errorsDiv.innerHTML = `<div class="success-message">
            ✓ Successfully connected to WorldQuant BRAIN<br>
            <strong>Username:</strong> ${brainSession.username}<br>
            <strong>Operators loaded:</strong> ${brainOperators ? brainOperators.length : 0}<br>
            <em>Data fields will be loaded when needed.</em>
        </div>`;
    
        // Auto-hide the message after 5 seconds
        setTimeout(() => {
            if (errorsDiv.innerHTML.includes('Successfully connected')) {
                errorsDiv.innerHTML = '';
            }
        }, 5000);
    }
}

// Show login status message
function showLoginStatus(message, type) {
    const statusDiv = document.getElementById('brainLoginStatus');
    statusDiv.textContent = message;
    statusDiv.className = `login-status ${type}`;
}

// Check if connected to BRAIN
function isConnectedToBrain() {
    return brainSession !== null && brainSessionId !== null;
}

// Get all available operators (fetch on-demand)
async function getAllOperators() {
    if (!brainOperators && isConnectedToBrain()) {
        try {
            brainOperators = await getUserOperators();
        } catch (error) {
            console.error('Failed to fetch operators on-demand:', error);
            return [];
        }
    }
    return brainOperators || [];
}

// Get loaded operators synchronously (for UI components)
function getLoadedOperators() {
    return brainOperators || [];
}

// Get all available data fields (fetch on-demand)
async function getAllDataFields() {
    if (!brainDataFields && isConnectedToBrain()) {
        try {
            brainDataFields = await getDataFields();
        } catch (error) {
            console.error('Failed to fetch data fields on-demand:', error);
            return [];
        }
    }
    return brainDataFields || [];
}

// Get operators by category (with on-demand loading)
async function getOperatorsByCategory(category) {
    const operators = await getAllOperators();
    return operators.filter(op => op.category === category);
}

// Search operators (with on-demand loading)
async function searchOperators(searchTerm) {
    const operators = await getAllOperators();
    const term = searchTerm.toLowerCase();
    return operators.filter(op => 
        op.name.toLowerCase().includes(term) || 
        op.category.toLowerCase().includes(term)
    );
}

// Search data fields (with on-demand loading)
async function searchDataFields(searchTerm) {
    const dataFields = await getAllDataFields();
    const term = searchTerm.toLowerCase();
    return dataFields.filter(field => 
        field.id.toLowerCase().includes(term) || 
        field.description.toLowerCase().includes(term)
    );
}

// Logout from BRAIN
async function logoutFromBrain() {
    if (brainSessionId) {
        try {
            await fetch(`${PROXY_BASE}/api/logout`, {
                method: 'POST',
                headers: {
                    'Session-ID': brainSessionId
                }
            });
        } catch (error) {
            console.warn('Failed to logout from proxy server:', error);
        }
    }
    
    // 禁用Alpha Backtester按钮
    const alphaBacktesterBtn = document.getElementById('alphaBacktesterBtn');
    if (alphaBacktesterBtn) {
        alphaBacktesterBtn.disabled = true;
    }
    
    // Clear local session data
    brainSession = null;
    brainSessionId = null;
    brainOperators = null;
    brainDataFields = null;
    
    // Clear localStorage
    localStorage.removeItem('brain_session_id');
    
    // Update UI
    const connectBtn = document.getElementById('connectToBrain');
    connectBtn.textContent = 'Connect to BRAIN';
    connectBtn.className = 'btn btn-brain';
}

// Check session validity on page load
async function checkSessionValidity() {
    if (brainSessionId) {
        try {
            const response = await fetch(`${PROXY_BASE}/api/status`, {
                method: 'GET',
                headers: {
                    'Session-ID': brainSessionId
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.valid) {
                    brainSession = { authenticated: true, username: data.username };
                    // Update UI to show connected state
                    updateConnectedState();
                } else {
                    // Session expired, clear it
                    localStorage.removeItem('brain_session_id');
                    brainSessionId = null;
                }
            }
        } catch (error) {
            console.warn('Failed to check session validity:', error);
        }
    }
    
    // 更新Alpha Backtester按钮状态
    const alphaBacktesterBtn = document.getElementById('alphaBacktesterBtn');
    if (alphaBacktesterBtn) {
        alphaBacktesterBtn.disabled = !isConnectedToBrain();
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', checkSessionValidity);

// Export functions for use in other modules
window.brainAPI = {
    openBrainLoginModal,
    closeBrainLoginModal,
    authenticateBrain,
    isConnectedToBrain,
    getAllOperators,
    getAllDataFields,
    getDataFields,
    getOperatorsByCategory,
    searchOperators,
    searchDataFields,
    logoutFromBrain,
    getLoadedOperators
};