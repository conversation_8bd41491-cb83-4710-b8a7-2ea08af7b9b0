/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* Container and Layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;

}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px ;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    position: relative;
    min-height: 140px;
    width: 100%;

}

header h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

.header-buttons {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-end;
    max-width: 300px; 
}

.header-buttons-row {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-end;
}

.header-buttons .btn {
    padding: 8px 16px;
    font-size: 14px;
    white-space: nowrap;
}

.btn-brain {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-brain:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.btn-brain.connected {
    background: #4caf50;
    border-color: #4caf50;
    color: white;
}

.btn-brain.connected:hover {
    background: #45a049;
    border-color: #45a049;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    margin-top: 20px;
    height: calc(100vh - 400px);
    min-height: 600px;
}

/* Editor Section */
.editor-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.editor-header h2 {
    color: #333;
    font-size: 1.5em;
    margin-right: auto;
}

.auto-check-indicator {
    color: #48bb78;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.editor-actions {
    display: flex;
    gap: 10px;
}

/* Editor Wrapper */
.editor-wrapper {
    display: flex;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    overflow: hidden;
    background: #fafbfc;
    position: relative;
    height: 500px;
    min-height: 400px;
}

.line-numbers {
    background: #f0f3f6;
    color: #6b7280;
    padding: 12px 10px;
    text-align: right;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 16px;
    line-height: 1.6;
    user-select: none;
    border-right: 1px solid #e0e6ed;
    white-space: pre;
    overflow-y: auto;
    overflow-x: hidden;
    min-width: 40px;
    flex-shrink: 0;
    min-height: 100%;
}

/* Hide scrollbars but keep functionality */
.line-numbers::-webkit-scrollbar,
.highlighted-text::-webkit-scrollbar,
.expression-editor::-webkit-scrollbar {
    width: 0;
    height: 0;
}

.editor-container {
    position: relative;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 400px;
}

.highlighted-text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 12px 15px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 16px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
    pointer-events: none;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 100%;
}

/* 确保编辑器文本与高亮层字符宽度一致 */
#highlightedText, #expressionEditor {
    font-family: 'Consolas', 'Monaco', monospace !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    letter-spacing: 0 !important;
    word-spacing: 0 !important;
}

mark.template-tag {
  color: #1e88e5 !important;
  background: transparent !important;
}

/* 变量标签高亮样式 - 使用与其他位置一致的蓝色 */
.template-tag, .highlighted-text .template-brackets, .detected-template-tag {
  color: #1e88e5 !important; /* 标准蓝色 */
  text-shadow: 0 0 0.5px #1e88e5, 0 0 0.5px #1e88e5; /* 模拟粗体效果 */
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  display: inline !important;
  white-space: pre-wrap !important;
  font-variant: inherit !important;
  font-feature-settings: inherit !important;
}

.template-tag-name {
    color: #1e88e5 !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    display: inline !important;
}

.template-bracket {
    color: #1e88e5 !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    display: inline !important;
}

/* 确保高亮样式优先级 */
#highlightedText .template-tag {
  color: #1e88e5 !important;
}


.expression-editor {
    position: relative;
    width: 100%;
    min-height: 100%;
    height: 100%;
    padding: 12px 15px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 16px;
    line-height: 1.6;
    border: none;
    outline: none;
    resize: none;
    background: transparent;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: transparent;
    caret-color: #333;
    z-index: 2;
    overflow-y: auto;
    overflow-x: hidden;
    display: block;
}

/* Template Preview */
.template-preview {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 10px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
}

.template-preview .preview-label {
    color: #6b7280;
    font-size: 12px;
    margin-bottom: 4px;
}

/* Grammar Errors */
.grammar-errors {
    margin-top: 15px;
}

.error-item {
    background: #fee;
    border-left: 4px solid #f44336;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 14px;
}

.error-item strong {
    color: #d32f2f;
}

.success-message {
    background: #e8f5e9;
    border-left: 4px solid #4caf50;
    padding: 10px 15px;
    border-radius: 4px;
    color: #2e7d32;
}

.info-message {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 10px 15px;
    border-radius: 4px;
    color: #1565c0;
    font-size: 14px;
    line-height: 1.5;
}

/* Info Panel */
.info-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info-panel h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.grammar-rules {
    list-style: none;
    margin-bottom: 30px;
}

.grammar-rules li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.grammar-rules code {
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', monospace;
    color: #667eea;
}

/* Template List */
.template-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.template-item {
    background: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 35px;
    gap: 8px;
}

.template-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.template-item.configured::before {
    content: "✓";
    position: absolute;
    left: 12px;
    top: 16px;
    color: #48bb78;
    font-weight: bold;
}

.template-item.not-configured::before {
    content: "•";
    position: absolute;
    left: 12px;
    top: 16px;
    color: #9ca3af;
}

.template-name {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    margin-bottom: 4px;
}

.template-name:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.template-name .template-tag {
    color: #2563eb;
    font-weight: 600;
}

.template-name .template-brackets {
    color: #7c3aed;
    font-weight: 600;
}

.template-count {
    color: #48bb78;
    font-weight: 600;
    font-size: 13px;
    margin-left: 4px;
}

.template-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding-left: 8px;
}

.template-buttons .btn-small {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 45px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #48bb78;
    color: white;
}

.btn-secondary:hover {
    background: #38a169;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(72, 187, 120, 0.3);
}

.btn-outline {
    background: transparent;
    color: #6b7280;
    border: 1px solid #e0e6ed;
}

.btn-outline:hover {
    background: #f5f7fa;
    color: #333;
}

/* Header button overrides for visibility on purple background */
header .btn-outline {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(10px);
}

header .btn-outline:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.6);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
    position: relative;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e6ed;
    flex-shrink: 0;
}

#brainDataFieldsModal .modal-header {
    padding: 15px 20px;
}

.modal-header h3 {
    color: #333;
    margin: 0;
}

.close {
    color: #999;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    line-height: 1;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.modal-body p {
    margin-bottom: 15px;
    color: #666;
}

.variable-input {
    width: 100%;
    min-height: 100px;
    padding: 10px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    resize: vertical;
}

.variable-input:focus {
    outline: none;
    border-color: #667eea;
}

.modal-footer {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 0 0 10px 10px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #e0e6ed;
    flex-shrink: 0;
}

#brainDataFieldsModal .modal-footer {
    padding: 15px 20px;
}

#brainDataFieldsModal .modal-body {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    padding: 15px;
    min-height: 0;
}

/* Configuration Info Modal Specific Styles */
.config-info-content {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.config-info-item {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.config-info-item strong {
    color: #333;
    display: inline-block;
    min-width: 80px;
}

.config-info-values {
    background: #e3f2fd;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
    color: #1565c0;
    word-break: break-all;
}

.config-status-unconfigured {
    color: #f44336;
    font-style: italic;
}

.config-status-configured {
    color: #4caf50;
    font-weight: 600;
}

/* Form Styles for BRAIN Login */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
}

.login-status {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    min-height: 20px;
}

.login-status.loading {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #90caf9;
}

.login-status.success {
    background: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #81c784;
}

.login-status.error {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ef5350;
}

.login-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: none;
    cursor: not-allowed;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Shadow Suggestion */
.shadow-suggestion {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 16px;
    line-height: 1.6;
    color: #9ca3af;
    opacity: 0.5;
    position: relative;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
}

.shadow-hint {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    animation: fadeIn 0.2s ease;
}

/* Decoded Results */
.decoded-results {
    margin-top: 30px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.decoded-results h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 1.2em;
}

.results-list {
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
}

.result-item {
    padding: 8px 12px;
    margin-bottom: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.5;
    position: relative;
}

.result-item:hover {
    background: #e9ecef;
}

.result-number {
    display: inline-block;
    width: 30px;
    color: #6b7280;
    font-weight: 600;
}

.result-expression {
    color: #333;
}

.copy-single {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    padding: 4px 8px;
    font-size: 11px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.result-item:hover .copy-single {
    opacity: 1;
}

.copy-single:hover {
    background: #5a67d8;
}

.results-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.results-info {
    background: #e0f2fe;
    border: 1px solid #7dd3fc;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    color: #075985;
    font-size: 14px;
}

/* Results Search Container */
.results-search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8fafc;
    border-radius: 6px;
    align-items: center;
}

.results-search-input {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
    transition: border-color 0.3s ease;
}

.results-search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.results-search-input::placeholder {
    color: #9ca3af;
}

/* Warning message style */
.warning-message {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    padding: 10px 15px;
    border-radius: 6px;
    color: #92400e;
    font-size: 14px;
    margin-bottom: 15px;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-item label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.setting-item .form-input {
    padding: 8px 10px;
    font-size: 13px;
}

@media (max-width: 600px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

/* Settings Modal Styles */
.settings-modal .modal-content {
    max-width: 900px;
    width: 90%;
    max-height: 90vh;
}

.settings-modal-content {
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    margin: 5vh auto;
}

.settings-modal-body {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: calc(90vh - 140px); /* Account for header and footer */
    overflow: hidden;
}

.settings-info {
    background: #f0f4f8;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #667eea;
    flex-shrink: 0;
}

.settings-note {
    color: #6b7280;
    font-size: 14px;
    margin-top: 5px;
}

.settings-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
}

.settings-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.settings-table thead {
    position: sticky;
    top: 0;
    background: #f5f7fa;
    z-index: 10;
}

.settings-table th {
    padding: 12px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e0e6ed;
    font-size: 14px;
}

.settings-table td {
    padding: 10px 12px;
    border-bottom: 1px solid #e5e7eb;
}

.settings-table tr:hover {
    background: #f9fafb;
}

.setting-value-input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
    transition: border-color 0.3s ease;
}

.setting-value-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-value-input[readonly] {
    background: #f5f5f5;
    cursor: not-allowed;
}

.setting-value-input::placeholder {
    color: #9ca3af;
    font-size: 13px;
}

.setting-type-select {
    padding: 6px 10px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 13px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.setting-type-select:focus {
    outline: none;
    border-color: #667eea;
}

.setting-type-select[disabled] {
    background: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
}

.remove-setting-btn {
    padding: 4px 8px;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.remove-setting-btn:hover {
    background: #dc2626;
}

.add-setting-container {
    padding: 15px;
    text-align: center;
    background: #f9fafb;
}

.settings-preview {
    background: #e0f2fe;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #7dd3fc;
    flex-shrink: 0;
}

.preview-header {
    font-size: 14px;
    color: #075985;
}

#totalCombinations {
    font-weight: 600;
    color: #0c4a6e;
}

/* Custom setting row styles */
.custom-setting-row td:first-child {
    position: relative;
}

.custom-setting-row input[type="text"]:first-child {
    width: 100%;
}

.setting-name-input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e0e6ed;
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
    transition: border-color 0.3s ease;
}

.setting-name-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Decode Section */
.decode-section {
    display: none;
}

/* Navigation Tabs */
.page-navigation {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    background: white;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: #f5f7fa;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 15px;
    font-weight: 500;
    color: #666;
    position: relative;
}

.nav-tab:hover {
    background: #e9ecef;
    color: #333;
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.nav-icon {
    font-size: 20px;
}

.nav-text {
    font-size: 14px;
}

.nav-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #f44336;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
    font-weight: 600;
}

/* Page Container */
.page-container {
    position: relative;
    min-height: 600px;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
    height: 100%;
}

/* Next Step Hint */
.next-step-hint {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e6ed;
    text-align: center;
}

.next-step-hint .btn {
    font-size: 16px;
    padding: 10px 20px;
}

/* Decode Content */
.decode-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 0 auto;
}

.decode-content h2 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.search-space {
    font-size: 0.65em;
    color: #667eea;
    font-weight: normal;
    background: #f0f3ff;
    padding: 4px 12px;
    border-radius: 20px;
    border: 1px solid #d4d9ff;
}

.template-status {
    background: #f5f7fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.decode-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.btn-large {
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
}

.random-iteration-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.random-count-input {
    width: 100px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    border: 2px solid #e0e6ed;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.random-count-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.random-count-input::placeholder {
    color: #9ca3af;
}

.decode-info {
    margin-top: 30px;
    padding: 20px;
    background: #e3f2fd;
    border-radius: 8px;
}

.decode-info p {
    margin: 10px 0;
    color: #1565c0;
}

/* Results Content */
.results-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.results-content h2 {
    margin-bottom: 20px;
    color: #333;
}

/* Navigation Buttons */
.navigation-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e6ed;
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    .info-panel {
        margin-top: 20px;
    }
    
    header h1 {
        font-size: 1.8em;
    }
    
    .header-buttons {
        position: static;
        margin-top: 15px;
        justify-content: center;
        align-items: center;
    }
    
    .header-buttons-row {
        justify-content: center;
    }
    
    .header-buttons .btn {
        font-size: 13px;
        padding: 6px 12px;
    }
    
    .editor-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .editor-header h2 {
        margin-bottom: 10px;
    }
    
    .auto-check-indicator {
        font-size: 12px;
        margin-bottom: 10px;
    }
    
    .editor-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    /* Navigation responsive */
    .page-navigation {
        flex-wrap: wrap;
        padding: 5px;
    }
    
    .nav-tab {
        min-width: calc(33.33% - 10px);
        padding: 10px 5px;
        font-size: 13px;
    }
    
    .nav-icon {
        font-size: 18px;
    }
    
    .nav-text {
        font-size: 12px;
        display: none;
    }
    
    /* Show only icons on very small screens */
    @media (max-width: 480px) {
        .nav-tab {
            min-width: auto;
            flex: 1;
            padding: 12px 8px;
        }
    }
    
    .decode-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .random-iteration-container {
        width: 100%;
        justify-content: space-between;
    }
    
    .btn-large {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .decode-content h2 {
        font-size: 1.3em;
        justify-content: center;
        text-align: center;
    }
    
    .search-space {
        font-size: 0.55em;
        padding: 3px 10px;
    }
}

/* Responsive adjustments for template items */
@media (max-width: 350px) {
    .template-item {
        padding-left: 12px;
    }
    
    .template-item.configured::before,
    .template-item.not-configured::before {
        position: static;
        display: inline-block;
        margin-right: 8px;
    }
    
    .template-name {
        justify-content: flex-start;
    }
    
    .template-buttons {
        justify-content: flex-start;
        padding-left: 0;
    }
}

/* BRAIN Operators Modal Styles */
.brain-choose-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e6ed;
    text-align: center;
}

.search-section {
    margin-bottom: 15px;
}

.category-filter {
    margin-bottom: 15px;
}

.category-filter label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.operator-actions {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.operators-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 15px;
    background: #fafbfc;
}

.operator-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    margin-bottom: 5px;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: 1px solid #e0e6ed;
}

.operator-item:hover {
    background: #f0f3f6;
}

.operator-item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
}

.operator-checkbox {
    margin-right: 10px;
}

.operator-info {
    flex: 1;
}

.operator-name {
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: 600;
    color: #2563eb;
}

.operator-category {
    font-size: 12px;
    color: #6b7280;
    margin-left: 10px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
}

.selected-operators {
    background: #f8f9fa;
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    padding: 10px;
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
}

.selected-item {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', monospace;
    display: flex;
    align-items: center;
    gap: 5px;
}

.selected-item .remove-btn {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    cursor: pointer;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selected-item .remove-btn:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Data Fields Modal Styles */
#brainDataFieldsModal.modal {
    padding: 0;
}

#brainDataFieldsModal .modal-content {
    width: calc(100vw - 40px);
    max-width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    max-height: calc(100vh - 40px);
    margin: 20px auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.data-fields-params {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    flex-shrink: 0;
}

.params-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: end;
}

.param-group {
    min-width: 120px;
    flex: 1;
}

.param-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.loading-message {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 16px;
}

.data-fields-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    min-height: 0;
}

.data-fields-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px;
    flex-shrink: 0;
}

.filter-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.filter-label span {
    white-space: nowrap;
}

.filter-options {
    display: flex;
    gap: 20px;
    flex: 1;
}

.filter-options label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #555;
}

.filter-options input[type="checkbox"] {
    cursor: pointer;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.data-fields-stats {
    font-size: 13px;
    color: #666;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 5px;
    text-align: center;
    font-weight: 500;
    flex-shrink: 0;
}

.data-fields-stats span {
    margin: 0 10px;
}

.data-fields-table-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #e0e6ed;
    border-radius: 8px;
    background: white;
    position: relative;
    min-height: 0;
    max-height: calc(100vh - 400px);
}

.data-fields-table {
    width: 100%;
    min-width: 900px;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
}

.data-fields-table thead {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-fields-table thead tr {
    background: #f8f9fa;
}

.data-fields-table th {
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e0e6ed;
    background: #f8f9fa;
    font-size: 13px;
}

.data-fields-table th:first-child {
    text-align: center;
}

.data-fields-table .header-row th {
    position: relative;
    padding-right: 30px;
}

.sort-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    padding: 2px 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.sort-btn:hover {
    background: #e0e6ed;
    color: #333;
}

.sort-btn.asc::after {
    content: '↑';
}

.sort-btn.desc::after {
    content: '↓';
}

.filter-row th {
    padding: 6px 5px;
    background: #f0f3f6;
    border-bottom: 1px solid #e0e6ed;
}

.column-filter {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.column-filter:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.column-filter.active {
    background: #e3f2fd;
    border-color: #2196f3;
}

.range-filter {
    display: flex;
    align-items: center;
    gap: 5px;
}

.column-filter-min,
.column-filter-max {
    width: 48%;
    padding: 4px 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 11px;
    background: white;
}

.column-filter-min:focus,
.column-filter-max:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.data-fields-table tbody tr {
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.data-fields-table tbody tr:hover {
    background: #f8f9fa;
}

.data-fields-table tbody tr.selected {
    background: #e3f2fd;
}

.data-fields-table td {
    padding: 8px;
    border-bottom: 1px solid #f0f3f6;
    vertical-align: top;
}

.data-fields-table td:first-child {
    text-align: center;
}

.data-field-checkbox {
    cursor: pointer;
    transform: scale(1.1);
}

.data-field-id {
    font-weight: 600;
    color: #1976d2;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
}

.data-field-description {
    color: #333;
    font-size: 13px;
    line-height: 1.4;
}

.data-field-type {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    color: #666;
    background: #f0f3f6;
    padding: 2px 6px;
    border-radius: 3px;
}

.data-field-coverage {
    font-weight: 500;
    color: #059862;
}

.data-field-count {
    color: #666;
    font-size: 13px;
}

.selected-data-fields {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e0e6ed;
    flex-shrink: 0;
    max-height: 100px;
    overflow-y: auto;
}

.selected-data-fields strong {
    display: block;
    margin-bottom: 10px;
    color: #333;
}

/* Responsive adjustments for data fields modal */
@media (max-width: 768px) {
    #brainDataFieldsModal .modal-content {
        width: calc(100vw - 20px);
        height: calc(100vh - 20px);
        margin: 10px auto;
    }
    
    .params-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .param-group {
        min-width: auto;
        width: 100%;
    }
    
    .data-fields-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-options {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }
    
    .filter-actions {
        width: 100%;
        justify-content: center;
    }
    
    .data-fields-table-container {
        max-height: calc(100vh - 350px);
        overflow-y: auto;
        overflow-x: auto;
    }
    
    .data-fields-table {
        font-size: 12px;
        min-width: 600px;
    }
    
    .data-fields-table th,
    .data-fields-table td {
        padding: 8px 5px;
    }
    
    .data-field-id {
        font-size: 12px;
    }
    
    .data-field-description {
        font-size: 12px;
    }
    
    .data-field-type,
    .data-field-coverage,
    .data-field-count {
        font-size: 11px;
    }
}

/* Progress Bar Styles */
.test-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar-fill {
    height: 100%;
    background-color: #28a745;
    transition: width 0.3s ease;
}

.test-results {
    padding: 20px 0;
}

.test-result-item {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.test-result-item.success {
    border-color: #28a745;
    background-color: #f0fff4;
}

.test-result-item.error {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.test-result-item.running {
    border-color: #ffc107;
    background-color: #fffbf0;
}

.test-result-expression {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    margin-bottom: 10px;
    word-break: break-all;
}

.test-result-status {
    font-weight: bold;
    margin-bottom: 5px;
}

.test-result-message {
    color: #666;
    font-size: 13px;
}

.test-summary {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.test-summary-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.test-summary-stat {
    flex: 1;
    text-align: center;
}

.test-summary-stat-value {
    font-size: 24px;
    font-weight: bold;
}

.test-summary-stat-label {
    font-size: 14px;
    color: #666;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

/* Paper Analysis Styles */
.paper-analysis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.api-key-section,
.upload-section,
.analysis-options,
.results-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* File Upload Styles */
.file-upload-container {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.file-upload-container:hover,
.file-upload-container.drag-over {
    border-color: #007bff;
}

.file-input {
    display: none;
}

.file-label {
    display: block;
    cursor: pointer;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.upload-text {
    font-size: 16px;
    font-weight: 500;
}

.file-types {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.file-info {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Analysis Options */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.option-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
}

/* Results Section */
.results-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.tab-btn {
    padding: 8px 16px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #007bff;
    color: white;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Keywords Display */
.keyword-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 8px;
}

.keyword-score {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Summary Display */
.summary-text {
    line-height: 1.6;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    white-space: pre-wrap;
}

/* Related Works Display */
.related-work-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 15px;
}

.related-work-item h3 {
    margin-bottom: 5px;
}

.formula-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.formula-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.formula-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.formula-number {
    font-weight: bold;
    color: #6c757d;
    font-size: 14px;
}

.formula-type {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.formula-type.definition {
    background: #e3f2fd;
    color: #1976d2;
}

.formula-type.theorem {
    background: #f3e5f5;
    color: #7b1fa2;
}

.formula-type.lemma {
    background: #e8f5e9;
    color: #388e3c;
}

.formula-type.equation {
    background: #fff3e0;
    color: #f57c00;
}

.formula-type.inequality {
    background: #fce4ec;
    color: #c2185b;
}

.formula-type.identity {
    background: #e0f2f1;
    color: #00796b;
}

.formula-type.other {
    background: #f5f5f5;
    color: #616161;
}

.formula-chinese {
    font-size: 14px;
    color: #495057;
    background: #fff3cd;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #ffc107;
    line-height: 1.4;
}

.formula-chinese strong {
    color: #856404;
}

.formula-expression {
    background: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 15px;
    text-align: center;
    font-size: 18px;
    overflow-x: auto;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
}

.formula-description {
    font-size: 15px;
    color: #495057;
    margin-bottom: 10px;
    font-weight: 500;
}

.formula-variables {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.variables-list {
    margin-top: 8px;
}

.variables-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.var-tab-btn {
    padding: 5px 12px;
    border: 1px solid #007bff;
    background: white;
    color: #007bff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.var-tab-btn:hover {
    background: #e3f2fd;
}

.var-tab-btn.active {
    background: #007bff;
    color: white;
}

.variables-content {
    position: relative;
}

.variables-list {
    margin-top: 0;
}

.variables-list.chinese-vars,
.variables-list.english-vars {
    display: none;
}

.variables-list.chinese-vars.active,
.variables-list.english-vars.active {
    display: block;
}

.variable-item {
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.variable-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.variable-symbol {
    min-width: 80px;
    font-size: 16px;
    display: inline-block;
}

.variable-separator {
    color: #6c757d;
    margin: 0 5px;
    font-weight: 500;
}

.variable-desc {
    flex: 1;
    color: #495057;
    font-size: 14px;
}

.variable-item code {
    background: #e3f2fd;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: bold;
    color: #1976d2;
    margin-right: 8px;
}

.formula-context {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 10px;
}

.similarity {
    color: #6c757d;
    font-size: 0.9em;
    margin-top: 5px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-message {
    margin-top: 15px;
    font-size: 1.1em;
    color: #333;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
}

.notification.error {
    background-color: #dc3545;
}

.notification.warning {
    background-color: #ffc107;
    color: #212529;
}

@keyframes slideIn {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Export Section */
.export-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

/* Unified Templates Section */
.templates-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.templates-header h3 {
    color: #333;
    font-size: 1.2em;
    margin: 0;
}

.template-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

#toggleTemplateView {
    background-color: #4a5568;
    border-color: #4a5568;
}

#toggleTemplateView:hover {
    background-color: #2d3748;
    border-color: #2d3748;
}

/* Template buttons container */
.all-template-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

#exampleTemplateButtons,
#customTemplateButtons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
}

/* Base template button styles */
.btn-template {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    border: 1px solid;
    position: relative;
}

/* Example template styles - purple theme */
.btn-template-example {
    background: #f3f0ff;
    color: #6b46c1;
    border-color: #d8b4fe;
}

.btn-template-example:hover {
    background: #7c3aed;
    color: white;
    border-color: #7c3aed;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(124, 58, 237, 0.3);
}

/* Custom template styles - blue theme */
.btn-template-custom {
    background: #eff6ff;
    color: #1e40af;
    border-color: #bfdbfe;
    padding-right: 35px;
}

.btn-template-custom:hover {
    background: #2563eb;
    color: white;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
}

/* Delete button for custom templates */
.btn-template-custom .delete-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 16px;
    padding: 2px 6px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.btn-template-custom:hover .delete-btn {
    opacity: 1;
}

.btn-template-custom .delete-btn:hover {
    color: #c82333;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 3px;
}

.custom-template-info {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    margin-top: 10px;
}

/* 减少Alpha Backtester和Run Simulator按钮宽度 */
#alphaBacktesterBtn, #runSimulator {
    width: 45%; 
    text-align: center;
}
#alphaBacktesterBtn{
    width: 50%; 
    text-align: center;
}

/* 新增样式 */
.array-cell { max-width: 300px; overflow: auto; }
.nested-array { padding-left: 20px; }
.wrap-text { white-space: pre-wrap; word-break: break-all; }

/* 文件名显示样式 */
.filename {
    color: #666;
    font-size: 0.9em;
    margin-left: 8px;
}